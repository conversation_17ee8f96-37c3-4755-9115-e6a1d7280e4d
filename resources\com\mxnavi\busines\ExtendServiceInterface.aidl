// ExtendServiceInterface.aidl
package com.mxnavi.busines;
import  com.mxnavi.busines.IServiceCallBack;
import  com.mxnavi.busines.entity.ResponseData;
import  com.mxnavi.busines.IStatusChangedListener;
import  com.mxnavi.busines.entity.PageOpreaData;
import  com.mxnavi.busines.entity.RequestRouteExModel;
import  com.mxnavi.busines.entity.ModifyNaviViaModel;
interface ExtendServiceInterface {

    /**
     * 通用请求
     * protocol:json通信协议
     */
    void doRequest(String protocol , IServiceCallBack iServiceCallBack);

    /**
     * 设置家的地址,如果已经设置则返回地址信息
     * needJumpToNavi: false 不跳转，只是监听变化。true 跳转，监听变化
     */
    void setHomeAddressResult(boolean needJumpToNavi, IServiceCallBack iServiceCallBack);

    /**
     * 设置公司的地址,如果已经设置则返回地址信息
     * needJumpToNavi: false 不跳转，只是监听变化。true 跳转，监听变化
     */
    void setCompanyAddressResult(boolean needJumpToNavi, IServiceCallBack iServiceCallBack);

     /**
     * 预约导航
     * info: 目的地数据
     */
    void navigationPlanningRoute(String info ,IServiceCallBack iServiceCallBack);

    /**
    *  车企账号绑定检查请求
    */
    void carltdCheckBindRequest(String userCenterId ,IServiceCallBack callback);
     /**
      *  车企账号快速登录请求
      */
    void carltdLoginRequest(IServiceCallBack callback);

    /**
      *  车企账号绑定请求
      */
    void carltdBindRequest(String userCenterId , IServiceCallBack observer);

    /**
     * 车企账号解除绑定请求
     */
    void carltdUnBindRequest(String userCenterId ,IServiceCallBack callback);

   /**
    * 拉起导航app 显示登陆二维码
    */
    void startMXnaviApp(IServiceCallBack callback);

    /**
     * 获取登录信息 如果登录过返回登陆信息
     */
    void getMXnaviAppLoginResult(IServiceCallBack callback);
    /**
     * 关键字搜索
     */
    void searchNearByPoi(String nearby,String keyword,boolean needShow,int intentionType, int index, IServiceCallBack callback);

    /**
     * 获取导航状态
     */
    int getNaviState();

    /**
     * 获取当前位置信息
     */
    void getCurrentLocationWithOpera(int type,int operaType,IServiceCallBack callback);

    /**
     * 获取导航类型 RouteGuideType
     */
    ResponseData getNaviType();

    /**
     * 添加状态更改监听
     */
    void addStatusChangedListener(IStatusChangedListener statusListener);

    /**
     * 移除状态更改监听
     */
    void removeStatusChangedListener(IStatusChangedListener statusListener);

    /**
     * 发送surface用于投屏
     */
    void sendSurfaceForProjectionScreen(int screenType, in Surface surface, String mapConfig);

    /**
     *  结束仪表投屏
     */
    void stopInstrumentProjection(int screenType);

    /**
     *  ID_IP_INFORMAT_TRANS_STS信号请求
     */
    void transStsRequest();

    /**
     * 获取导航state NaviState
     */
    void getNaviStateResult(IServiceCallBack iServiceCallBack);

    void getInstrumentGuideTipUpdated(IServiceCallBack iServiceCallBack);

    void getInstrumentOfflineTurnIcon(IServiceCallBack iServiceCallBack);

    void getInstrumentTurnDistance(IServiceCallBack iServiceCallBack);

    void getInstrumentTurnDistanceDetail(IServiceCallBack iServiceCallBack);

    void getInstrumentNextRoadName(IServiceCallBack iServiceCallBack);

    void getInstrumentExitData(IServiceCallBack iServiceCallBack);

    void getInstrumentNextGuideInfo(IServiceCallBack iServiceCallBack);

    void getInstrumentOfflineCrossTurnIcon(IServiceCallBack iServiceCallBack);

    void getInstrumentToggleCrossUpdateProgress(IServiceCallBack iServiceCallBack);

    void getInstrumentLaneInfo(IServiceCallBack iServiceCallBack);

    void getInstrumentSwitchNavVision(IServiceCallBack iServiceCallBack);

    /**
    * 获取 剩余时间+距离
    */
    void getRouteRemain(IServiceCallBack callback);

    /**
    * 获取剩余红绿灯
    */
    void getTrafficLight(IServiceCallBack callback);

    /**
    * 获取到达时间
    */
    void getNaviArrivalTime(IServiceCallBack callback);

    /**
     * 获取路况条信息
     */
    void getTmcInfo(IServiceCallBack callback);

    /**
    * 获取服务区数据
    */
    void ServiceAreaUpdated(IServiceCallBack callback);

    /**
     * 获取前方道路信息
     */
    void getGuideTREvent(IServiceCallBack callback);

    /**
     * 获取目的地定位
     */
    ResponseData getRouteEndPoint();

    /**
     * 获取面板信息
     */
    void getPanelData(IServiceCallBack callback);

    /**
     * 根据经纬度获取地址
     */
    void getAddressByCoordinate(double lon, double lat, IServiceCallBack callback);

    void naviToPoi(boolean isRefresh ,String poiString,IServiceCallBack callback);

    void navigation(String poiString,boolean isShowMutilPage,IServiceCallBack callback);

    void goToNearbyGasStation(String name,IServiceCallBack callback);

    /**
    *退出导航中
    */
    void cancelNavigation(IServiceCallBack callback);

    int mapOpera(int actionType,int operaType,IServiceCallBack callBack);

    void searchTraffic(int type,String keyword,IServiceCallBack callback);

    int cancelNavi();

    int goHomeOrCompany(int destType, int directNavi);

    int goHomeOrCompany2(int destType,IServiceCallBack callback);

    int collectCurrentPos(int type);

    void collectByPoi(int type,String poiString,IServiceCallBack callback);

    int getSpeakMode();

    int setSpeakMode(int speakMode);

    int getScaleLevel();

    int lookOverView();

    int goTeamTrip();

    int goSetting();

    int changePreference(int preferenceId);

    ResponseData getDestInfo();

    ResponseData getRemainDistance();

    ResponseData getRemainTime();

    void searchAlongRoute(String keyword,boolean needShow,IServiceCallBack callback);

    int accountOpera(int actionType);

    int naviOpera(int actionType,int operaType,IServiceCallBack callBack);

    ResponseData getAccountStatus();

    int sendWxPosition(String posStr);

    ResponseData getCityInfo();

    void calculateRoad(int position,IServiceCallBack callback);

    int selectRouteToNavi(int position,IServiceCallBack callback);

    void backToMap(int type,IServiceCallBack callback);

    int pageOpera(in PageOpreaData data,IServiceCallBack callback);

    int setVolumeMute(boolean isMute);

    int isVolumeMute();

    void requestRouteEx(in RequestRouteExModel reqRouteExModel,IServiceCallBack callback);

    void requestAddPass(in ModifyNaviViaModel model,IServiceCallBack callback);

    void requestGuideInfo(IServiceCallBack callback);

    ResponseData getHomeOrCompanyData(int type);

    int getPassPointNum();

    ResponseData isExistInCollect(String poiName);

    void setNaviScreen(IServiceCallBack callback);

    int setDayNightStyle(int style);

    void getSearchHistory(IServiceCallBack callback);

    void getFavoriteList(IServiceCallBack callback);

    void getRouteSummaryList(IServiceCallBack callback);

    int goFavorite();

    int getNaviStage();

    int isInNavi();

    void getEndPoint(IServiceCallBack callback);

    void lightFigureSwitch(int type, IServiceCallBack callback);

    void deleteViaPoint(int index,IServiceCallBack callback);

    /**
    * 用于区分语音在后台退出导航
    */
    int cancelNaviBack();

    /**
    *  设置仪表白天，黑夜模式
    *  showMode: 1 白天  2 黑夜
    */
    void setInstrumentShowMode(int showMode);

    /**
      *  恢复引导
      *  showMode: 1 刷新  2 恢复
      */
    void restoreNavigation(int type ,IServiceCallBack iServiceCallBack);

    /**
      *  主辅路切换
      */
    void switchParallelRoad(int type ,IServiceCallBack iServiceCallBack);

    /**
      *  路线切换
      */
    void switchRoute(IServiceCallBack callback);

    /**
     *  获取到达途经点
     */
    void getArrivedViaPoint(IServiceCallBack callback);

    /**
     *  获取在线转向图标
     */
    void getInstrumentOnlineTurnIcon(IServiceCallBack iServiceCallBack);

    /**
     *  获取在线下个路口转向图标
     */
    void getInstrumentOnlineCrossTurnIcon(IServiceCallBack iServiceCallBack);

    /**
     *  关闭仪表地图遮罩
     */
    void closeInstrumentMapMask(IServiceCallBack iServiceCallBack);

    /**
     *  获取当前pageid
     */
    int getPageId();
}
