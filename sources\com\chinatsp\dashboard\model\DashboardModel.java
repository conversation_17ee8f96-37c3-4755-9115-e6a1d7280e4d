package com.chinatsp.dashboard.model;

import android.content.Context;
import com.chinatsp.dashboard.callback.CarServiceCallback;
import com.chinatsp.dashboard.callback.NaviCallback;
import com.chinatsp.dashboard.utils.Logcat;

/* loaded from: classes.dex */
public abstract class DashboardModel implements CarServiceCallback, NaviCallback {
    private static final String TAG = "DashboardModel";
    protected volatile BaseServiceModel mCarServiceModel;
    protected volatile YandexNaviModel mNaviModel;

    protected abstract BaseServiceModel createServiceModel(Context context, CarServiceCallback carServiceCallback);

    public void handleMediaInfo(int i, int i2, String str, String str2) {
    }

    public void handleMediaInfo(int i, int i2, String str, String str2, String str3, int i3, int i4) {
    }

    public void handleMediaSource(String str) {
    }

    public void register(Context context) {
        if (this.mCarServiceModel == null) {
            this.mCarServiceModel = createServiceModel(context, this);
            this.mCarServiceModel.register();
        }
        if (this.mNaviModel == null) {
            this.mNaviModel = new YandexNaviModel(context, this);
            this.mNaviModel.init();
        }
    }

    public void unregister() {
        if (this.mCarServiceModel != null) {
            this.mCarServiceModel.unregister();
            this.mCarServiceModel = null;
        }
        if (this.mNaviModel != null) {
            this.mNaviModel.release();
            this.mNaviModel = null;
        }
    }

    @Override // com.chinatsp.dashboard.callback.CarServiceCallback
    public void onCarServiceConnected() {
        Logcat.d();
    }

    @Override // com.chinatsp.dashboard.callback.NaviCallback
    public void requestSendNaviInfo(int[] iArr) {
        BaseServiceModel baseServiceModel;
        synchronized (this) {
            baseServiceModel = this.mCarServiceModel;
        }
        if (baseServiceModel != null) {
            baseServiceModel.sendNaviInfo(iArr);
        }
    }

    @Override // com.chinatsp.dashboard.callback.NaviCallback
    public void requestSendNextRoadName(byte[] bArr) {
        BaseServiceModel baseServiceModel;
        synchronized (this) {
            baseServiceModel = this.mCarServiceModel;
        }
        if (baseServiceModel != null) {
            baseServiceModel.sendNextRoadName(bArr);
        }
    }

    @Override // com.chinatsp.dashboard.callback.NaviCallback
    public void naviStateChange(boolean z) {
        Logcat.d(TAG, "naviStateChange isNaviEnable:" + z + " , mCarServiceModel:" + this.mCarServiceModel);
    }

    public void appReturnToNormal() {
        Logcat.d(TAG, "appReturnToNormal");
        if (this.mCarServiceModel != null) {
            this.mCarServiceModel.appReturnToNormal();
        }
    }

    public void appShutdown() {
        Logcat.d(TAG, "appShutdown");
        if (this.mCarServiceModel != null) {
            this.mCarServiceModel.appShutdown();
        }
    }

    public boolean requestFitnessAnimChange(int i, boolean z, boolean z2, boolean z3, boolean z4, int i2, int i3, int i4, int i5, int i6, int i7, String str, String str2) {
        BaseServiceModel baseServiceModel;
        synchronized (this) {
            baseServiceModel = this.mCarServiceModel;
        }
        if (baseServiceModel != null) {
            boolean requestFitnessAnimChange = baseServiceModel.requestFitnessAnimChange(i, z, z2, z3, z4, i2, i3, i4, i5, i6, i7, str, str2);
            Logcat.d(TAG, "requestFitnessAnimChange fitnessType:" + requestFitnessAnimChange);
            return requestFitnessAnimChange;
        }
        Logcat.d(TAG, "requestFitnessAnimChange fail. mCarServiceModel is null. mFitnessBean:");
        return false;
    }
}
