.class public Lcom/chinatsp/dashboard/model/YandexNaviModel;
.super Ljava/lang/Object;
.source "YandexNaviModel.java"

# interfaces
.implements Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;


# static fields
.field private static final TAG:Ljava/lang/String; = "YandexNaviModel"

.field private static final YANDEX_NAVI_PACKAGE:Ljava/lang/String; = "ru.yandex.yandexnavi"


# instance fields
.field private final mContext:Landroid/content/Context;

.field private mNaviCallback:Lcom/chinatsp/dashboard/callback/NaviCallback;

.field private volatile interactiveProtocol:Lcom/chinatsp/dashboard/model/IInteractiveProtocol;

.field private final mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private isYandexNaviInstalled:Z


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/chinatsp/dashboard/callback/NaviCallback;)V
    .locals 2

    .line 23
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 20
    new-instance v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    .line 21
    iput-boolean v1, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->isYandexNaviInstalled:Z

    .line 24
    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mContext:Landroid/content/Context;

    .line 25
    invoke-virtual {p0, p2}, Lcom/chinatsp/dashboard/model/YandexNaviModel;->setNaviCallback(Lcom/chinatsp/dashboard/callback/NaviCallback;)V

    return-void
.end method

.method private checkYandexNaviInstalled()V
    .locals 3

    .line 37
    :try_start_0
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mContext:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v0

    .line 38
    const-string v1, "ru.yandex.yandexnavi"

    const/4 v2, 0x1

    invoke-virtual {v0, v1, v2}, Landroid/content/pm/PackageManager;->getPackageInfo(Ljava/lang/String;I)Landroid/content/pm/PackageInfo;

    .line 39
    iput-boolean v2, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->isYandexNaviInstalled:Z

    .line 40
    const-string v0, "YandexNaviModel"

    const-string v1, "Yandex Navigator is installed"

    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_0
    .catch Landroid/content/pm/PackageManager$NameNotFoundException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 42
    const/4 v0, 0x0

    iput-boolean v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->isYandexNaviInstalled:Z

    .line 43
    const-string v0, "YandexNaviModel"

    const-string v1, "Yandex Navigator is not installed"

    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->w(Ljava/lang/String;Ljava/lang/String;)V

    :goto_0
    return-void
.end method

.method private naviStateChange(Z)V
    .locals 3

    .line 75
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "naviStateChange isRunning:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v1, " , mNaviCallback:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 76
    iget-object v1, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mNaviCallback:Lcom/chinatsp/dashboard/callback/NaviCallback;

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    move v1, v2

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    const-string v1, " , interactiveProtocol:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 77
    iget-object v1, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->interactiveProtocol:Lcom/chinatsp/dashboard/model/IInteractiveProtocol;

    if-eqz v1, :cond_1

    const/4 v2, 0x1

    :cond_1
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 75
    const-string v1, "YandexNaviModel"

    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 80
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mContext:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    .line 81
    if-eqz p1, :cond_2

    const/4 v1, 0x0

    goto :goto_1

    :cond_2
    const/4 v1, 0x1

    :goto_1
    const-string v2, "navi_status_to_vehicle_control"

    invoke-static {v0, v2, v1}, Landroid/provider/Settings$System;->putInt(Landroid/content/ContentResolver;Ljava/lang/String;I)Z

    .line 84
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mNaviCallback:Lcom/chinatsp/dashboard/callback/NaviCallback;

    if-eqz v0, :cond_3

    .line 85
    invoke-interface {v0, p1}, Lcom/chinatsp/dashboard/callback/NaviCallback;->naviStateChange(Z)V

    .line 89
    :cond_3
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->interactiveProtocol:Lcom/chinatsp/dashboard/model/IInteractiveProtocol;

    if-eqz v0, :cond_4

    .line 90
    invoke-interface {v0, p1}, Lcom/chinatsp/dashboard/model/IInteractiveProtocol;->naviStateChange(Z)V

    :cond_4
    return-void
.end method


# virtual methods
.method public init()V
    .locals 3

    .line 33
    const-string v0, "YandexNaviModel"

    const-string v1, "YandexNaviModel init"

    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 34
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/YandexNaviModel;->checkYandexNaviInstalled()V

    .line 35
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mContext:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    const-string v1, "navi_status_to_vehicle_control"

    const/4 v2, 0x1

    invoke-static {v0, v1, v2}, Landroid/provider/Settings$System;->putInt(Landroid/content/ContentResolver;Ljava/lang/String;I)Z

    return-void
.end method

.method public isNaviRunning()Z
    .locals 2

    .line 51
    iget-boolean v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->isYandexNaviInstalled:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    .line 52
    :goto_0
    const-string v1, "YandexNaviModel"

    invoke-static {v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    return v0
.end method

.method public isYandexNaviInstalled()Z
    .locals 1

    .line 99
    iget-boolean v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->isYandexNaviInstalled:Z

    return v0
.end method

.method public release()V
    .locals 2

    .line 46
    const-string v0, "YandexNaviModel"

    const-string v1, "YandexNaviModel release"

    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 47
    const/4 v0, 0x0

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mNaviCallback:Lcom/chinatsp/dashboard/callback/NaviCallback;

    .line 48
    iput-object v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->interactiveProtocol:Lcom/chinatsp/dashboard/model/IInteractiveProtocol;

    return-void
.end method

.method public setInteractiveProtocol(Lcom/chinatsp/dashboard/model/IInteractiveProtocol;)V
    .locals 2

    .line 95
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "setInteractiveProtocol protocol:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "YandexNaviModel"

    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 96
    monitor-enter p0

    .line 97
    :try_start_0
    iput-object p1, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->interactiveProtocol:Lcom/chinatsp/dashboard/model/IInteractiveProtocol;

    .line 98
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public setNaviCallback(Lcom/chinatsp/dashboard/callback/NaviCallback;)V
    .locals 0

    .line 29
    iput-object p1, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mNaviCallback:Lcom/chinatsp/dashboard/callback/NaviCallback;

    return-void
.end method

.method public setNaviRunning(Z)V
    .locals 2

    .line 56
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    .line 57
    iget-object v1, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v1, p1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    if-eq v0, p1, :cond_0

    .line 60
    const-string v0, "YandexNaviModel"

    const-string v1, "Navi state changed"

    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 61
    invoke-direct {p0, p1}, Lcom/chinatsp/dashboard/model/YandexNaviModel;->naviStateChange(Z)V

    :cond_0
    return-void
.end method

.method public updateNaviState()V
    .locals 1

    .line 103
    iget-boolean v0, p0, Lcom/chinatsp/dashboard/model/YandexNaviModel;->isYandexNaviInstalled:Z

    if-eqz v0, :cond_0

    .line 104
    const-string v0, "YandexNaviModel"

    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    :cond_0
    return-void
.end method

.method public forceNaviActive(Z)V
    .locals 2

    .line 108
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "forceNaviActive: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "YandexNaviModel"

    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 109
    invoke-virtual {p0, p1}, Lcom/chinatsp/dashboard/model/YandexNaviModel;->setNaviRunning(Z)V

    return-void
.end method
