# 🚀 Инструкция по установке модифицированного Dashboard

## 📁 Структура файлов

### Основные smali файлы (заменить):
```
smali/com/chinatsp/dashboard/model/
├── MediaConstants.smali          ✅ (заменить)
├── DashboardModel.smali          ✅ (заменить)
├── InteractiveModel.smali        ✅ (заменить)
└── YandexNaviModel.smali         🆕 (добавить новый)

smali/com/example/mxextend/
└── ExtendApi.smali               ✅ (заменить)

smali/com/chinatsp/dashboard/receiver/
└── YandexNaviControlReceiver.smali  🆕 (добавить новый)
```

### Скрипты управления:
- `activate_navi.bat` - для Windows
- `activate_navi.sh` - для Linux/Mac

## 🔧 Пошаговая установка

### 1. Подготовка
```bash
# Создайте резервную копию оригинального APK
cp original_dashboard.apk dashboard_backup.apk

# Разберите APK
apktool d original_dashboard.apk -o dashboard_project
```

### 2. Замена файлов
Замените следующие файлы в папке `dashboard_project/`:

**MediaConstants.smali** → `smali/com/chinatsp/dashboard/utils/MediaConstants.smali`
**DashboardModel.smali** → `smali/com/chinatsp/dashboard/model/DashboardModel.smali`
**InteractiveModel.smali** → `smali/com/chinatsp/dashboard/model/InteractiveModel.smali`
**ExtendApi.smali** → `smali/com/example/mxextend/ExtendApi.smali`

### 3. Добавление новых файлов
**YandexNaviModel.smali** → `smali/com/chinatsp/dashboard/model/YandexNaviModel.smali`
**YandexNaviControlReceiver.smali** → `smali/com/chinatsp/dashboard/receiver/YandexNaviControlReceiver.smali`

### 4. Изменение AndroidManifest.xml
Добавьте в `resources/AndroidManifest.xml` перед закрывающим тегом `</application>`:

```xml
<receiver
    android:name="com.chinatsp.dashboard.receiver.YandexNaviControlReceiver"
    android:exported="true">
    <intent-filter>
        <action android:name="com.chinatsp.dashboard.FORCE_NAVI_ACTIVE"/>
    </intent-filter>
</receiver>
```

### 5. Сборка APK
```bash
# Соберите модифицированный APK
apktool b dashboard_project -o dashboard_modified.apk

# Подпишите APK (замените на ваш ключ)
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore your_key.keystore dashboard_modified.apk your_alias

# Или используйте apksigner (Android SDK)
apksigner sign --ks your_key.keystore dashboard_modified.apk
```

### 6. Установка
```bash
# Удалите старую версию (если нужно)
adb uninstall com.chinatsp.dashboard

# Установите модифицированную версию
adb install dashboard_modified.apk
```

## 🎮 Использование

### 🚀 ПРИНУДИТЕЛЬНАЯ АКТИВАЦИЯ ПРИБОРНОЙ ПАНЕЛИ:
```bash
# Windows
activate_navi.bat on

# Linux/Mac
./activate_navi.sh on
```
**Результат:** Приборка НЕМЕДЛЕННО станет черной (режим трансляции)

### 🛑 ДЕАКТИВАЦИЯ ПРИБОРНОЙ ПАНЕЛИ:
```bash
# Windows
activate_navi.bat off

# Linux/Mac
./activate_navi.sh off
```
**Результат:** Приборка вернется к обычному отображению

### Альтернативный способ (через adb):
```bash
# Включить трансляцию
adb shell am broadcast -a com.chinatsp.dashboard.FORCE_NAVI_ACTIVE --ez navi_state true

# Выключить трансляцию
adb shell am broadcast -a com.chinatsp.dashboard.FORCE_NAVI_ACTIVE --ez navi_state false
```

## 📋 Мониторинг логов
```bash
# Просмотр логов Dashboard
adb logcat | grep -E "(YandexNaviModel|DashboardModel|InteractiveModel)"

# Только логи Yandex модели
adb logcat | grep YandexNaviModel
```

## 🔍 Проверка работы

1. **Установите модифицированный APK**
2. **Запустите Яндекс.Навигатор** (если установлен)
3. **Выполните команду активации**: `activate_navi.bat on`
4. **Проверьте приборную панель** - она должна переключиться в режим трансляции
5. **Для деактивации**: `activate_navi.bat off`

## ⚠️ Важные замечания

- **Яндекс.Навигатор должен быть установлен** на устройстве
- **Приложение Dashboard должно быть запущено** в фоне
- **Команды работают независимо** от того, запущен ли Яндекс.Навигатор
- **Можно принудительно активировать** режим трансляции в любое время

## 🐛 Устранение неполадок

### Если трансляция не активируется:
1. Проверьте логи: `adb logcat | grep YandexNaviModel`
2. Убедитесь, что Dashboard запущен: `adb shell ps | grep dashboard`
3. Проверьте, что Receiver зарегистрирован в манифесте

### Если приборная панель не реагирует:
1. Перезапустите Dashboard: `adb shell am force-stop com.chinatsp.dashboard`
2. Попробуйте команду еще раз
3. Проверьте системные настройки: `adb shell settings list system | grep navi`

## 🎯 Результат

После успешной установки вы сможете:
- ✅ Принудительно активировать режим трансляции на приборной панели
- ✅ Управлять отображением независимо от навигационного приложения
- ✅ Использовать любое содержимое на приборной панели
- ✅ Контролировать процесс через простые команды
