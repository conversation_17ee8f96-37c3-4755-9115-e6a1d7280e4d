package com.example.mxextend;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.ServiceConnection;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import androidx.core.app.NotificationCompat;
import com.example.mxextend.entity.AddressModel;
import com.example.mxextend.entity.CarltdLoginResultBean;
import com.example.mxextend.entity.CarltdRequestInfoModel;
import com.example.mxextend.entity.CityInfo;
import com.example.mxextend.entity.CollectInfoModel;
import com.example.mxextend.entity.ExtendBaseModel;
import com.example.mxextend.entity.ExtendErrorModel;
import com.example.mxextend.entity.GuideInfoModel;
import com.example.mxextend.entity.HistoricaRouteInfo;
import com.example.mxextend.entity.HomeCompanyItemBean;
import com.example.mxextend.entity.LocationDetail;
import com.example.mxextend.entity.LocationDetailModel;
import com.example.mxextend.entity.LocationInfo;
import com.example.mxextend.entity.PoiBean;
import com.example.mxextend.entity.RouteResult;
import com.example.mxextend.entity.RouteSummaryModel;
import com.example.mxextend.entity.SearchResultModel;
import com.example.mxextend.entity.TrafficInfoModel;
import com.example.mxextend.listener.IAccountListener;
import com.example.mxextend.listener.IExtendCallback;
import com.example.mxextend.listener.IExtendJsonCallback;
import com.example.mxextend.listener.IExtendListener;
import com.example.mxextend.listener.IHomeOrCompanyListener;
import com.example.mxextend.listener.ILocationChangedListener;
import com.example.mxextend.listener.ILocationUpdateCallBack;
import com.example.mxextend.listener.IManualVoiceInteractionListener;
import com.example.mxextend.listener.IPageChangedListener;
import com.example.mxextend.listener.IRouteDataListener;
import com.example.mxextend.listener.ISearchDataListener;
import com.example.mxextend.listener.ISearchResultListener;
import com.example.mxextend.listener.IServiceConnectedListener;
import com.example.mxextend.listener.ISuggestionResultListener;
import com.example.mxextend.listener.IVoiceBtnCallBack;
import com.example.mxextend.listener.IWeatherInfoListener;
import com.example.mxextend.receiver.MxNaviStatusReceiver;
import com.example.mxextend.widget.DisplaySurfaceView;
import com.example.mxextend.widget.config.SurfaceMapConfig;
import com.google.gson.Gson;
import com.mxnavi.busines.ExtendServiceInterface;
import com.mxnavi.busines.IServiceCallBack;
import com.mxnavi.busines.IStatusChangedListener;
import com.mxnavi.busines.entity.ModifyNaviViaModel;
import com.mxnavi.busines.entity.PageOpreaData;
import com.mxnavi.busines.entity.RequestGuideInfoModel;
import com.mxnavi.busines.entity.RequestRouteExModel;
import com.mxnavi.busines.entity.RequestSearchData;
import com.mxnavi.busines.entity.ResponseData;
import com.mxnavi.busines.entity.ShowFrontTrafficModel;
import com.mxnavi.busines.entity.ShowTrafficModel;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.concurrent.CopyOnWriteArrayList;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: ExtendApi.kt */
@Metadata(d1 = {"\u0000\u009c\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0014\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\u0018\u0000 \u0090\u00022\u00020\u00012\u00020\u0002:\u0002\u0090\u0002B\u0007\b\u0012¢\u0006\u0002\u0010\u0003B\u0011\b\u0010\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005¢\u0006\u0002\u0010\u0006J\u0010\u0010P\u001a\u00020.2\u0006\u0010Q\u001a\u00020.H\u0016J\u0012\u0010R\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010\tH\u0016J\u0012\u0010U\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010\u001aH\u0016J\u0012\u0010V\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010 H\u0016J\u0012\u0010W\u001a\u00020S2\b\u0010X\u001a\u0004\u0018\u00010$H\u0016J\u0012\u0010Y\u001a\u00020S2\b\u0010Z\u001a\u0004\u0018\u00010&H\u0016J\u0012\u0010[\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u000102H\u0016J\u0012\u0010\\\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u000109H\u0016J\u0012\u0010]\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010CH\u0016J\u0012\u0010^\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010EH\u0016J\u0012\u0010_\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010GH\u0016J\u0012\u0010`\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010*H\u0016J\u0012\u0010a\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010KH\u0016J\u0012\u0010b\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010OH\u0016J<\u0010c\u001a\u00020.2\b\u0010d\u001a\u0004\u0018\u00010\u00112\b\u0010e\u001a\u0004\u0018\u00010\u00112\u0006\u0010f\u001a\u00020.2\u0006\u0010g\u001a\u00020h2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J \u0010l\u001a\u00020S2\u0006\u0010m\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\b\u0010o\u001a\u00020SH\u0016J \u0010p\u001a\u00020S2\u0006\u0010q\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020r\u0018\u00010jH\u0016J\b\u0010s\u001a\u00020.H\u0016J\b\u0010t\u001a\u00020.H\u0016J\u001a\u0010u\u001a\u00020S2\u0010\u0010i\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010n\u0018\u00010jH\u0016J$\u0010v\u001a\u00020S2\b\u0010w\u001a\u0004\u0018\u00010\u00112\u0010\u0010i\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010x\u0018\u00010jH\u0016J$\u0010y\u001a\u00020S2\b\u0010w\u001a\u0004\u0018\u00010\u00112\u0010\u0010i\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010x\u0018\u00010jH\u0016J\u001a\u0010z\u001a\u00020S2\u0010\u0010i\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010x\u0018\u00010jH\u0016J$\u0010{\u001a\u00020S2\b\u0010w\u001a\u0004\u0018\u00010\u00112\u0010\u0010i\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010x\u0018\u00010jH\u0016J\u0010\u0010|\u001a\u00020S2\u0006\u0010}\u001a\u00020.H\u0016J \u0010~\u001a\u00020.2\u0006\u0010\u007f\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0012\u0010\u0080\u0001\u001a\u00020.2\u0007\u0010\u0081\u0001\u001a\u00020.H\u0016J-\u0010\u0082\u0001\u001a\u00020.2\u0006\u0010m\u001a\u00020.2\n\u0010\u0083\u0001\u001a\u0005\u0018\u00010\u0084\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J#\u0010\u0085\u0001\u001a\u00020S2\u0007\u0010\u0086\u0001\u001a\u00020.2\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030\u0084\u0001\u0018\u00010jH\u0016J\u001a\u0010\u0087\u0001\u001a\u00020S2\u0007\u0010\u0088\u0001\u001a\u00020\u00112\u0006\u0010i\u001a\u00020\u000bH\u0016J\t\u0010\u0089\u0001\u001a\u00020.H\u0016J.\u0010\u008a\u0001\u001a\u00020S2\b\u0010\u008b\u0001\u001a\u00030\u008c\u00012\b\u0010\u008d\u0001\u001a\u00030\u008c\u00012\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030\u008e\u0001\u0018\u00010jH\u0016J\u001a\u0010\u008f\u0001\u001a\u00020S2\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030\u0090\u0001\u0018\u00010jH\u0016J\u001a\u0010\u0091\u0001\u001a\u00020S2\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030\u0084\u0001\u0018\u00010jH\u0016J\u0018\u0010\u0092\u0001\u001a\u00020S2\r\u0010i\u001a\t\u0012\u0005\u0012\u00030\u0084\u00010jH\u0016J\u001b\u0010\u0093\u0001\u001a\u00020S2\u0010\u0010i\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010k\u0018\u00010jH\u0016J \u0010\u0094\u0001\u001a\u00020S2\u0006\u0010m\u001a\u00020.2\r\u0010i\u001a\t\u0012\u0005\u0012\u00030\u0084\u00010jH\u0016J\u001c\u0010\u0095\u0001\u001a\u00020S2\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u0096\u0001\u0018\u00010jH\u0016J\t\u0010\u0097\u0001\u001a\u00020.H\u0016J\t\u0010\u0098\u0001\u001a\u00020.H\u0016J\t\u0010\u0099\u0001\u001a\u00020.H\u0016J\t\u0010\u009a\u0001\u001a\u00020.H\u0016J\t\u0010\u009b\u0001\u001a\u00020.H\u0016J\t\u0010\u009c\u0001\u001a\u00020.H\u0016J\u001c\u0010\u009d\u0001\u001a\u00020S2\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u009e\u0001\u0018\u00010jH\u0016J\t\u0010\u009f\u0001\u001a\u00020.H\u0016J\u001b\u0010 \u0001\u001a\u00020S2\u0010\u0010i\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010k\u0018\u00010jH\u0016J\t\u0010¡\u0001\u001a\u00020.H\u0016J\t\u0010¢\u0001\u001a\u00020.H\u0016J+\u0010£\u0001\u001a\u00020S2\u0007\u0010¤\u0001\u001a\u00020.2\u0007\u0010¥\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0019\u0010¦\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\t\u0010§\u0001\u001a\u00020.H\u0016J$\u0010¨\u0001\u001a\u00020S2\t\u0010©\u0001\u001a\u0004\u0018\u00010\u00112\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0011\u0010ª\u0001\u001a\u00020S2\u0006\u0010}\u001a\u00020.H\u0002J'\u0010«\u0001\u001a\u00020S2\t\u0010¬\u0001\u001a\u0004\u0018\u00010\u00112\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u00ad\u0001\u0018\u00010jH\u0016J\t\u0010®\u0001\u001a\u00020.H\u0016J\t\u0010¯\u0001\u001a\u00020.H\u0016J#\u0010°\u0001\u001a\u00020.2\b\u0010e\u001a\u0004\u0018\u00010\u00112\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J+\u0010°\u0001\u001a\u00020.2\b\u0010e\u001a\u0004\u0018\u00010\u00112\u0006\u0010f\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J!\u0010±\u0001\u001a\u00020S2\u0006\u0010m\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\t\u0010²\u0001\u001a\u00020.H\u0016J,\u0010³\u0001\u001a\u00020.2\u0006\u0010Q\u001a\u00020.2\u0007\u0010´\u0001\u001a\u00020.2\u0010\u0010i\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010n\u0018\u00010jH\u0016J*\u0010µ\u0001\u001a\u00020.2\u0006\u0010Q\u001a\u00020.2\u0007\u0010´\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J.\u0010¶\u0001\u001a\u00020S2\u0007\u0010·\u0001\u001a\u00020\"2\n\u0010\u0083\u0001\u001a\u0005\u0018\u00010\u0084\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J-\u0010¶\u0001\u001a\u00020S2\u0007\u0010¸\u0001\u001a\u00020\"2\t\u0010¹\u0001\u001a\u0004\u0018\u00010\u00112\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J.\u0010º\u0001\u001a\u00020S2\n\u0010\u0083\u0001\u001a\u0005\u0018\u00010\u0084\u00012\u0007\u0010»\u0001\u001a\u00020\"2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J&\u0010¼\u0001\u001a\u00020S2\n\u0010\u0083\u0001\u001a\u0005\u0018\u00010½\u00012\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030¾\u0001\u0018\u00010jH\u0016J%\u0010¿\u0001\u001a\u00020.2\n\u0010À\u0001\u001a\u0005\u0018\u00010Á\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J+\u0010¿\u0001\u001a\u00020.2\u0007\u0010Â\u0001\u001a\u00020.2\u0007\u0010Ã\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0013\u0010Ä\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010\tH\u0016J\u0013\u0010Å\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010\u001aH\u0016J\u0013\u0010Æ\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010 H\u0016J\u0013\u0010Ç\u0001\u001a\u00020S2\b\u0010X\u001a\u0004\u0018\u00010$H\u0016J\u0013\u0010È\u0001\u001a\u00020S2\b\u0010Z\u001a\u0004\u0018\u00010&H\u0016J\u0013\u0010É\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u000102H\u0016J\u0013\u0010Ê\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u000109H\u0016J\u0013\u0010Ë\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010CH\u0016J\u0013\u0010Ì\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010EH\u0016J\u0013\u0010Í\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010GH\u0016J\u0015\u0010Î\u0001\u001a\u00020S2\n\u0010Ï\u0001\u001a\u0005\u0018\u00010Ð\u0001H\u0002J\u0013\u0010Ñ\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010*H\u0016J\t\u0010Ò\u0001\u001a\u00020SH\u0002J\u0013\u0010Ó\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010KH\u0016J\u0013\u0010Ô\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010OH\u0016J%\u0010Õ\u0001\u001a\u00020.2\n\u0010Ö\u0001\u001a\u0005\u0018\u00010×\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J%\u0010Ø\u0001\u001a\u00020S2\b\u0010e\u001a\u0004\u0018\u00010\u00112\u0010\u0010i\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010k\u0018\u00010jH\u0016J(\u0010Ù\u0001\u001a\u00020.2\n\u0010Ú\u0001\u001a\u0005\u0018\u00010Û\u00012\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010Ü\u0001\u0018\u00010jH\u0016J#\u0010Ý\u0001\u001a\u00020S2\b\u0010e\u001a\u0004\u0018\u00010\u00112\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J%\u0010Þ\u0001\u001a\u00020.2\n\u0010Ö\u0001\u001a\u0005\u0018\u00010ß\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\"\u0010à\u0001\u001a\u00020S2\u0006\u0010m\u001a\u00020.2\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030á\u0001\u0018\u00010jH\u0016J%\u0010â\u0001\u001a\u00020S2\b\u0010e\u001a\u0004\u0018\u00010\u00112\u0010\u0010i\u001a\f\u0012\u0006\u0012\u0004\u0018\u00010k\u0018\u00010jH\u0016J-\u0010ã\u0001\u001a\u00020.2\b\u0010d\u001a\u0004\u0018\u00010\u00112\b\u0010e\u001a\u0004\u0018\u00010\u00112\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J%\u0010ä\u0001\u001a\u00020S2\n\u0010å\u0001\u001a\u0005\u0018\u00010æ\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J-\u0010ç\u0001\u001a\u00020S2\u0006\u0010m\u001a\u00020.2\u0007\u0010è\u0001\u001a\u00020\u00112\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010é\u0001\u0018\u00010jH\u0002J \u0010ê\u0001\u001a\u00020.2\u0007\u0010ë\u0001\u001a\u00020.2\f\u0010i\u001a\b\u0012\u0004\u0012\u00020n0jH\u0016J\u001d\u0010ì\u0001\u001a\u00020.2\b\u0010í\u0001\u001a\u00030\u008c\u00012\b\u0010î\u0001\u001a\u00030\u008c\u0001H\u0016J!\u0010ï\u0001\u001a\u00020S2\u0007\u0010ð\u0001\u001a\u00020\"2\r\u0010i\u001a\t\u0012\u0005\u0012\u00030ñ\u00010jH\u0016J\"\u0010ò\u0001\u001a\u00020S2\u0007\u0010ó\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J!\u0010ô\u0001\u001a\u00020S2\u0007\u0010ð\u0001\u001a\u00020\"2\r\u0010i\u001a\t\u0012\u0005\u0012\u00030ñ\u00010jH\u0016J\u0019\u0010õ\u0001\u001a\u00020S2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0012\u0010ö\u0001\u001a\u00020.2\u0007\u0010÷\u0001\u001a\u00020.H\u0016J!\u0010ø\u0001\u001a\u00020.2\u0006\u0010m\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\t\u0010ù\u0001\u001a\u00020SH\u0002J\u0013\u0010ú\u0001\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010MH\u0016J\"\u0010û\u0001\u001a\u00020.2\u0007\u0010ü\u0001\u001a\u00020\"2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J(\u0010ý\u0001\u001a\u00020.2\n\u0010þ\u0001\u001a\u0005\u0018\u00010ÿ\u00012\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010é\u0001\u0018\u00010jH\u0016J\u001e\u0010\u0080\u0002\u001a\u00020S2\u0007\u0010\u0081\u0002\u001a\u00020.2\n\u0010\u0082\u0002\u001a\u0005\u0018\u00010\u0083\u0002H\u0016J-\u0010\u0084\u0002\u001a\u00020.2\u0006\u0010m\u001a\u00020.2\u0007\u0010´\u0001\u001a\u00020.2\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u0084\u0001\u0018\u00010jH\u0016J(\u0010\u0085\u0002\u001a\u00020.2\n\u0010þ\u0001\u001a\u0005\u0018\u00010\u0086\u00022\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010é\u0001\u0018\u00010jH\u0016J\"\u0010\u0087\u0002\u001a\u00020.2\u0007\u0010¤\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J+\u0010\u0087\u0002\u001a\u00020.2\u0007\u0010¤\u0001\u001a\u00020.2\u0007\u0010¥\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u001c\u0010\u0088\u0002\u001a\u00020S2\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u0096\u0001\u0018\u00010jH\u0016J\u0012\u0010\u0089\u0002\u001a\u00020S2\u0007\u0010\u0081\u0002\u001a\u00020.H\u0016J\"\u0010\u008a\u0002\u001a\u00020S2\u0007\u0010´\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0019\u0010\u008b\u0002\u001a\u00020S2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\t\u0010\u008c\u0002\u001a\u00020SH\u0016J\t\u0010\u008d\u0002\u001a\u00020SH\u0016J\u0012\u0010\u008e\u0002\u001a\u00020S2\u0007\u0010\u008f\u0002\u001a\u00020,H\u0002R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004¢\u0006\u0002\n\u0000R\u001c\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\f\u0010\r\"\u0004\b\u000e\u0010\u000fR\u001c\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b\u0012\u0010\u0013\"\u0004\b\u0014\u0010\u0015R\u0010\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e¢\u0006\u0002\n\u0000R\u0014\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0019X\u0082\u0004¢\u0006\u0002\n\u0000R\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082\u000e¢\u0006\u0002\n\u0000R\u0014\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020 0\bX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\"X\u0082\u000e¢\u0006\u0002\n\u0000R\u0014\u0010#\u001a\b\u0012\u0004\u0012\u00020$0\bX\u0082\u0004¢\u0006\u0002\n\u0000R\u0014\u0010%\u001a\b\u0012\u0004\u0012\u00020&0\bX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010'\u001a\u00020(X\u0082\u0004¢\u0006\u0002\n\u0000R\u0014\u0010)\u001a\b\u0012\u0004\u0012\u00020*0\u0019X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020,X\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020.X\u0082\u000e¢\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u000200X\u0082\u0004¢\u0006\u0002\n\u0000R\u0014\u00101\u001a\b\u0012\u0004\u0012\u0002020\bX\u0082\u0004¢\u0006\u0002\n\u0000R\u001a\u00103\u001a\u00020.X\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b4\u00105\"\u0004\b6\u00107R\u0014\u00108\u001a\b\u0012\u0004\u0012\u0002090\bX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010:\u001a\u00020.X\u0082\u000e¢\u0006\u0002\n\u0000R\u0010\u0010;\u001a\u0004\u0018\u00010<X\u0082\u000e¢\u0006\u0002\n\u0000R\u001a\u0010=\u001a\u00020\"X\u0086\u000e¢\u0006\u000e\n\u0000\u001a\u0004\b>\u0010?\"\u0004\b@\u0010AR\u0014\u0010B\u001a\b\u0012\u0004\u0012\u00020C0\bX\u0082\u0004¢\u0006\u0002\n\u0000R\u0014\u0010D\u001a\b\u0012\u0004\u0012\u00020E0\bX\u0082\u0004¢\u0006\u0002\n\u0000R\u0014\u0010F\u001a\b\u0012\u0004\u0012\u00020G0\bX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010H\u001a\u00020IX\u0082\u0004¢\u0006\u0002\n\u0000R\u0014\u0010J\u001a\b\u0012\u0004\u0012\u00020K0\bX\u0082\u0004¢\u0006\u0002\n\u0000R\u0010\u0010L\u001a\u0004\u0018\u00010MX\u0082\u000e¢\u0006\u0002\n\u0000R\u0014\u0010N\u001a\b\u0012\u0004\u0012\u00020O0\bX\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006\u0091\u0002"}, d2 = {"Lcom/example/mxextend/ExtendApi;", "Lcom/example/mxextend/IExtendApi;", "Lcom/example/mxextend/ExtendConstants;", "()V", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "accountListeners", "Ljava/util/LinkedList;", "Lcom/example/mxextend/listener/IAccountListener;", "cacheExtendCallBack", "Lcom/example/mxextend/listener/IExtendJsonCallback;", "getCacheExtendCallBack", "()Lcom/example/mxextend/listener/IExtendJsonCallback;", "setCacheExtendCallBack", "(Lcom/example/mxextend/listener/IExtendJsonCallback;)V", "cacheProtocol", "", "getCacheProtocol", "()Ljava/lang/String;", "setCacheProtocol", "(Ljava/lang/String;)V", "displaySurfaceView", "Lcom/example/mxextend/widget/DisplaySurfaceView;", "extendListeners", "Ljava/util/concurrent/CopyOnWriteArrayList;", "Lcom/example/mxextend/listener/IExtendListener;", "extendServiceInterface", "Lcom/mxnavi/busines/ExtendServiceInterface;", "gson", "Lcom/google/gson/Gson;", "homeOrCompanyListeners", "Lcom/example/mxextend/listener/IHomeOrCompanyListener;", "isBind", "", "locationListeners", "Lcom/example/mxextend/listener/ILocationChangedListener;", "locationUpdateCallBacks", "Lcom/example/mxextend/listener/ILocationUpdateCallBack;", "mDeathRecipient", "Landroid/os/IBinder$DeathRecipient;", "mServiceConnectedListeners", "Lcom/example/mxextend/listener/IServiceConnectedListener;", "mServiceConnection", "Landroid/content/ServiceConnection;", "mShowMode", "", "mainHandler", "Landroid/os/Handler;", "manualVoiceInteractionListeners", "Lcom/example/mxextend/listener/IManualVoiceInteractionListener;", "naviStateResultcode", "getNaviStateResultcode", "()I", "setNaviStateResultcode", "(I)V", "pageChangedListeners", "Lcom/example/mxextend/listener/IPageChangedListener;", "pageStatus", "receiver", "Lcom/example/mxextend/receiver/MxNaviStatusReceiver;", "reconnect", "getReconnect", "()Z", "setReconnect", "(Z)V", "routeDataListeners", "Lcom/example/mxextend/listener/IRouteDataListener;", "searchDataListeners", "Lcom/example/mxextend/listener/ISearchDataListener;", "searchResultListeners", "Lcom/example/mxextend/listener/ISearchResultListener;", "statusChangedListener", "Lcom/mxnavi/busines/IStatusChangedListener$Stub;", "suggestionResultListeners", "Lcom/example/mxextend/listener/ISuggestionResultListener;", "voiceClickListener", "Lcom/example/mxextend/listener/IVoiceBtnCallBack;", "weatherInfoListeners", "Lcom/example/mxextend/listener/IWeatherInfoListener;", "accountOpera", "actionType", "addAccountChangedListener", "", "listener", "addExtendListener", "addHomeOrCompanyChangedListener", "addLocationChangedListener", "locationChangedListener", "addLocationUpdateCallBack", "callBack", "addManualVoiceInteractionListener", "addPageChangedListener", "addRouteDataListener", "addSearchDataListener", "addSearchResultListener", "addServiceConnectedListener", "addSuggestDataListener", "addWeatherInfoListenerListener", "aroundSearch", "nearbyKey", "searchKey", "searchAction", "distance", "", "callback", "Lcom/example/mxextend/listener/IExtendCallback;", "Lcom/example/mxextend/entity/SearchResultModel;", "backToMap", "type", "Lcom/example/mxextend/entity/ExtendBaseModel;", "bindMxExtService", "calculateRoad", "i", "Lcom/example/mxextend/entity/RouteResult;", "cancelNavi", "cancelNaviBack", "cancelNavigation", "carltdBindRequest", "userCenterId", "Lcom/example/mxextend/entity/CarltdRequestInfoModel;", "carltdCheckBindRequest", "carltdLoginRequest", "carltdUnBindRequest", "changeInstrumentShowMode", "showMode", "changeNaviRoutePrefer", "strategy", "changePreference", "preferenceId", "collectByPoi", "info", "Lcom/example/mxextend/entity/LocationInfo;", "deleteViaPoint", "index", "doRequest", "protocol", "getAccountStatus", "getAddressByCoordinate", "lon", "", "lat", "Lcom/example/mxextend/entity/AddressModel;", "getCityInfo", "Lcom/example/mxextend/entity/CityInfo;", "getDestInfo", "getEndPoint", "getFavoriteList", "getHomeOrCompanyData", "getMXnaviAppLoginResult", "Lcom/example/mxextend/entity/CarltdLoginResultBean;", "getNaviStage", "getNaviState", "getNaviType", "getPassPointNum", "getRemainDistance", "getRemainTime", "getRouteSummaryList", "Lcom/example/mxextend/entity/RouteSummaryModel;", "getScaleLevel", "getSearchHistoryData", "getSpeakMode", "goFavorite", "goHomeOrCompany", "destType", "directNavi", "goSetting", "goTeamTrip", "goToNearbyGasStation", "nearbyName", "initDisplaySurfaceView", "isExistInCollect", "name", "Lcom/example/mxextend/entity/CollectInfoModel;", "isInNavi", "isVolumeMute", "keywordSearch", "lightFigureSwitch", "lookOverView", "mapOpera", "operaType", "naviOpera", "naviToPoi", "isRefres", "isRefresh", "poiName", NotificationCompat.CATEGORY_NAVIGATION, "isShowMutilPage", "navigationPlanningRoute", "Lcom/example/mxextend/entity/LocationDetailModel;", "Lcom/example/mxextend/entity/LocationDetail;", "pageOprea", "data", "Lcom/mxnavi/busines/entity/PageOpreaData;", "pageId", "action", "removeAccountChangedListener", "removeExtendListener", "removeHomeOrCompanyChangedListener", "removeLocationChangedListener", "removeLocationUpdateCallBack", "removeManualVoiceInteractionListener", "removePageChangedListener", "removeRouteDataListener", "removeSearchDataListener", "removeSearchResultListener", "removeSelfFromParent", "child", "Landroid/view/View;", "removeServiceConnectedListener", "removeStatusChangedListener", "removeSuggestDataListener", "removeWeatherInfoListenerListener", "requestAddPass", "model", "Lcom/mxnavi/busines/entity/ModifyNaviViaModel;", "requestAlongRouteData", "requestGuideInfo", "requestGuideInfoModel", "Lcom/mxnavi/busines/entity/RequestGuideInfoModel;", "Lcom/example/mxextend/entity/GuideInfoModel;", "requestPoiData", "requestRouteEx", "Lcom/mxnavi/busines/entity/RequestRouteExModel;", "restoreNavigation", "Lcom/example/mxextend/entity/HistoricaRouteInfo;", "searchAlongRoute", "searchNearByPoi", "searchPoi", "requestSearchData", "Lcom/mxnavi/busines/entity/RequestSearchData;", "searchTraffic", "keyWord", "Lcom/example/mxextend/entity/TrafficInfoModel;", "selectRoute", "selectId", "sendWxPosition", "longitude", "latitude", "setCompanyAddressResult", "needJumpToNavi", "Lcom/example/mxextend/entity/HomeCompanyItemBean;", "setDayNightStyle", "style", "setHomeAddressResult", "setNaviScreen", "setSpeakMode", "speakMode", "setSpecialPoi", "setStatusChangedListener", "setVoiceBtnCallBack", "setVolumeMute", "isMute", "showFrontTraffic", "showTrafficModel", "Lcom/mxnavi/busines/entity/ShowFrontTrafficModel;", "showInstrumentProjection", "screenType", "view", "Landroid/view/ViewGroup;", "showMyLocation", "showTraffic", "Lcom/mxnavi/busines/entity/ShowTrafficModel;", "specialPoiNavi", "startMXnaviAppResult", "stopInstrumentProjection", "switchParallelRoad", "switchRoute", "transStsRequest", "unBindMxExtService", "unbindService", "conn", "Companion", "NaviClient_release"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: classes.dex */
public final class ExtendApi implements IExtendApi, ExtendConstants {
    private static final String ACTION_BIND_EXTERNAL_SERVICE = "com.mxnavi.remote_service";
    private static final String SERVER_PACKAGE_NAME = "ru.yandex.yandexnavi";
    public static final String VERSION = "1.0.31";
    private IExtendJsonCallback cacheExtendCallBack;
    private String cacheProtocol;
    private Context context;
    private DisplaySurfaceView displaySurfaceView;
    private ExtendServiceInterface extendServiceInterface;
    private boolean isBind;
    private int naviStateResultcode;
    private MxNaviStatusReceiver receiver;
    private boolean reconnect;
    private IVoiceBtnCallBack voiceClickListener;
    private static final String TAG = ExtendApi.class.getSimpleName() + ", ver.  1.0.31";
    private final CopyOnWriteArrayList<IServiceConnectedListener> mServiceConnectedListeners = new CopyOnWriteArrayList<>();
    private final CopyOnWriteArrayList<IExtendListener> extendListeners = new CopyOnWriteArrayList<>();
    private final LinkedList<IAccountListener> accountListeners = new LinkedList<>();
    private final LinkedList<ILocationChangedListener> locationListeners = new LinkedList<>();
    private final LinkedList<IRouteDataListener> routeDataListeners = new LinkedList<>();
    private final LinkedList<ISearchDataListener> searchDataListeners = new LinkedList<>();
    private final LinkedList<ISearchResultListener> searchResultListeners = new LinkedList<>();
    private final LinkedList<ISuggestionResultListener> suggestionResultListeners = new LinkedList<>();
    private final LinkedList<ILocationUpdateCallBack> locationUpdateCallBacks = new LinkedList<>();
    private final LinkedList<IPageChangedListener> pageChangedListeners = new LinkedList<>();
    private final LinkedList<IHomeOrCompanyListener> homeOrCompanyListeners = new LinkedList<>();
    private final LinkedList<IWeatherInfoListener> weatherInfoListeners = new LinkedList<>();
    private final LinkedList<IManualVoiceInteractionListener> manualVoiceInteractionListeners = new LinkedList<>();
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private Gson gson = new Gson();
    private int mShowMode = 1;
    private int pageStatus = -1;
    private final ServiceConnection mServiceConnection = new ExtendApi$mServiceConnection$1(this);
    private final IBinder.DeathRecipient mDeathRecipient = new IBinder.DeathRecipient() { // from class: com.example.mxextend.ExtendApi$mDeathRecipient$1
        @Override // android.os.IBinder.DeathRecipient
        public void binderDied() {
            String str;
            ExtendServiceInterface extendServiceInterface;
            ExtendServiceInterface extendServiceInterface2;
            CopyOnWriteArrayList copyOnWriteArrayList;
            DisplaySurfaceView displaySurfaceView;
            String str2;
            boolean z;
            ServiceConnection serviceConnection;
            CopyOnWriteArrayList copyOnWriteArrayList2;
            CopyOnWriteArrayList copyOnWriteArrayList3;
            str = ExtendApi.TAG;
            Log.e(str, "binderDied");
            extendServiceInterface = ExtendApi.this.extendServiceInterface;
            if (extendServiceInterface == null) {
                return;
            }
            extendServiceInterface2 = ExtendApi.this.extendServiceInterface;
            Intrinsics.checkNotNull(extendServiceInterface2);
            extendServiceInterface2.asBinder().unlinkToDeath(this, 0);
            ExtendApi.this.extendServiceInterface = null;
            copyOnWriteArrayList = ExtendApi.this.mServiceConnectedListeners;
            if (copyOnWriteArrayList != null) {
                copyOnWriteArrayList2 = ExtendApi.this.mServiceConnectedListeners;
                if (copyOnWriteArrayList2.stream().count() > 0) {
                    copyOnWriteArrayList3 = ExtendApi.this.mServiceConnectedListeners;
                    Iterator it = copyOnWriteArrayList3.iterator();
                    while (it.hasNext()) {
                        ((IServiceConnectedListener) it.next()).onServiceDisconnected();
                    }
                }
            }
            displaySurfaceView = ExtendApi.this.displaySurfaceView;
            if (displaySurfaceView != null) {
                displaySurfaceView.isBind = false;
            }
            str2 = ExtendApi.TAG;
            StringBuilder append = new StringBuilder().append("binderDied set isBing: ");
            z = ExtendApi.this.isBind;
            Log.e(str2, append.append(z).toString());
            ExtendApi extendApi = ExtendApi.this;
            serviceConnection = extendApi.mServiceConnection;
            extendApi.unbindService(serviceConnection);
            ExtendApi.this.bindMxExtService();
        }
    };
    private final IStatusChangedListener.Stub statusChangedListener = new ExtendApi$statusChangedListener$1(this);

    @Override // com.example.mxextend.IExtendApi
    public void carltdLoginRequest(IExtendCallback<CarltdRequestInfoModel> callback) {
    }

    private ExtendApi() {
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final synchronized void unbindService(ServiceConnection conn) {
        if (this.isBind) {
            try {
                Context context = this.context;
                Intrinsics.checkNotNull(context);
                context.unbindService(conn);
            } catch (Exception e) {
                Log.e(TAG, "unbindService error: " + e.getMessage());
            }
            this.isBind = false;
        }
    }

    public ExtendApi(Context context) {
        this.context = context;
        if (context != null && this.receiver == null) {
            this.receiver = new MxNaviStatusReceiver();
            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction("com.mxnavi.extend.status.open");
            context.registerReceiver(this.receiver, intentFilter);
        }
        bindMxExtService();
    }

    @Override // com.example.mxextend.IExtendApi
    public void bindMxExtService() {
        if (this.isBind) {
            Log.i(TAG, "=============已经绑定服务过============");
            return;
        }
        Log.i(TAG, "=============bindMx ExtService============aar版本号 = 1.0.31");
        Intent intent = new Intent(ACTION_BIND_EXTERNAL_SERVICE);
        intent.setPackage("ru.yandex.yandexnavi");
        Context context = this.context;
        Intrinsics.checkNotNull(context);
        context.bindService(intent, this.mServiceConnection, 1);
    }

    @Override // com.example.mxextend.IExtendApi
    public void unBindMxExtService() {
        if (!this.isBind || this.extendServiceInterface == null) {
            return;
        }
        Log.e(TAG, "unBindMxExtService");
        unbindService(this.mServiceConnection);
        this.extendServiceInterface = null;
        this.isBind = false;
    }

    @Override // com.example.mxextend.IExtendApi
    public void addServiceConnectedListener(IServiceConnectedListener listener) {
        if (listener == null) {
            return;
        }
        CopyOnWriteArrayList<IServiceConnectedListener> copyOnWriteArrayList = this.mServiceConnectedListeners;
        Intrinsics.checkNotNull(copyOnWriteArrayList);
        if (copyOnWriteArrayList.contains(listener)) {
            return;
        }
        this.mServiceConnectedListeners.add(listener);
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeServiceConnectedListener(IServiceConnectedListener listener) {
        if (listener == null) {
            return;
        }
        CopyOnWriteArrayList<IServiceConnectedListener> copyOnWriteArrayList = this.mServiceConnectedListeners;
        Intrinsics.checkNotNull(copyOnWriteArrayList);
        if (copyOnWriteArrayList.contains(listener)) {
            this.mServiceConnectedListeners.remove(listener);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void setStatusChangedListener() {
        try {
            if (this.isBind) {
                ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
                if (extendServiceInterface != null) {
                    extendServiceInterface.addStatusChangedListener(this.statusChangedListener);
                }
                Log.d(TAG, "setStatusChangedListener");
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void removeStatusChangedListener() {
        try {
            if (this.isBind) {
                ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
                if (extendServiceInterface != null) {
                    extendServiceInterface.removeStatusChangedListener(this.statusChangedListener);
                }
                Log.d(TAG, "removeStatusChangedListener");
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    public final boolean getReconnect() {
        return this.reconnect;
    }

    public final void setReconnect(boolean z) {
        this.reconnect = z;
    }

    public final String getCacheProtocol() {
        return this.cacheProtocol;
    }

    public final void setCacheProtocol(String str) {
        this.cacheProtocol = str;
    }

    public final IExtendJsonCallback getCacheExtendCallBack() {
        return this.cacheExtendCallBack;
    }

    public final void setCacheExtendCallBack(IExtendJsonCallback iExtendJsonCallback) {
        this.cacheExtendCallBack = iExtendJsonCallback;
    }

    @Override // com.example.mxextend.IExtendApi
    public void doRequest(String protocol, IExtendJsonCallback callback) {
        Intrinsics.checkNotNullParameter(protocol, "protocol");
        Intrinsics.checkNotNullParameter(callback, "callback");
        String str = TAG;
        Log.d(str, "======================新增通用请求========================");
        Log.d(str, "======================isbind========================" + this.isBind + "  " + this.reconnect);
        boolean z = this.isBind;
        if (!z && !this.reconnect) {
            this.reconnect = true;
            this.cacheProtocol = protocol;
            this.cacheExtendCallBack = callback;
            bindMxExtService();
            return;
        }
        if (z) {
            try {
                ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
                Intrinsics.checkNotNull(extendServiceInterface);
                extendServiceInterface.doRequest(protocol, new ExtendApi$doRequest$1(this, callback));
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void setHomeAddressResult(boolean needJumpToNavi, final IExtendCallback<HomeCompanyItemBean> callback) {
        Intrinsics.checkNotNullParameter(callback, "callback");
        String str = TAG;
        Log.d(str, "======================设置家========================");
        Log.d(str, "======================isbind========================" + this.isBind);
        if (this.isBind) {
            try {
                ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
                Intrinsics.checkNotNull(extendServiceInterface);
                extendServiceInterface.setHomeAddressResult(needJumpToNavi, new IServiceCallBack.Stub() { // from class: com.example.mxextend.ExtendApi$setHomeAddressResult$1
                    @Override // com.mxnavi.busines.IServiceCallBack
                    public void onResponse(final int responseCode, final String response) throws RemoteException {
                        Handler handler;
                        Intrinsics.checkNotNullParameter(response, "response");
                        handler = ExtendApi.this.mainHandler;
                        final ExtendApi extendApi = ExtendApi.this;
                        final IExtendCallback<HomeCompanyItemBean> iExtendCallback = callback;
                        handler.post(new Runnable() { // from class: com.example.mxextend.ExtendApi$setHomeAddressResult$1$onResponse$1
                            @Override // java.lang.Runnable
                            public void run() {
                                String str2;
                                Gson gson;
                                str2 = ExtendApi.TAG;
                                Log.d(str2, "收到responseCode=" + responseCode + "  收到的家信息 response=" + response);
                                if (responseCode == 10000) {
                                    gson = extendApi.gson;
                                    Object fromJson = gson.fromJson(response, (Class<Object>) HomeCompanyItemBean.class);
                                    Intrinsics.checkNotNullExpressionValue(fromJson, "gson.fromJson<HomeCompan…                        )");
                                    iExtendCallback.success((HomeCompanyItemBean) fromJson);
                                    return;
                                }
                                iExtendCallback.onFail(new ExtendErrorModel(responseCode, response + ""));
                            }
                        });
                    }
                });
            } catch (RemoteException e) {
                e.printStackTrace();
                Log.d(TAG, "requestAddPass: exception=");
                this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$cno7kFLHMp3ZLMv0LOUj_aNfhsE
                    @Override // java.lang.Runnable
                    public final void run() {
                        ExtendApi.setHomeAddressResult$lambda$0(IExtendCallback.this);
                    }
                });
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setHomeAddressResult$lambda$0(IExtendCallback callback) {
        Intrinsics.checkNotNullParameter(callback, "$callback");
        callback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public void setCompanyAddressResult(boolean needJumpToNavi, final IExtendCallback<HomeCompanyItemBean> callback) {
        Intrinsics.checkNotNullParameter(callback, "callback");
        String str = TAG;
        Log.d(str, "======================设置公司========================");
        Log.d(str, "======================isbind========================" + this.isBind);
        if (this.isBind) {
            try {
                ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
                Intrinsics.checkNotNull(extendServiceInterface);
                extendServiceInterface.setCompanyAddressResult(needJumpToNavi, new IServiceCallBack.Stub() { // from class: com.example.mxextend.ExtendApi$setCompanyAddressResult$1
                    @Override // com.mxnavi.busines.IServiceCallBack
                    public void onResponse(final int responseCode, final String response) throws RemoteException {
                        Handler handler;
                        Intrinsics.checkNotNullParameter(response, "response");
                        handler = ExtendApi.this.mainHandler;
                        final ExtendApi extendApi = ExtendApi.this;
                        final IExtendCallback<HomeCompanyItemBean> iExtendCallback = callback;
                        handler.post(new Runnable() { // from class: com.example.mxextend.ExtendApi$setCompanyAddressResult$1$onResponse$1
                            @Override // java.lang.Runnable
                            public void run() {
                                String str2;
                                Gson gson;
                                str2 = ExtendApi.TAG;
                                Log.d(str2, "收到responseCode=" + responseCode + "  收到的公司信息 response=" + response);
                                if (responseCode == 10000) {
                                    gson = extendApi.gson;
                                    Object fromJson = gson.fromJson(response, (Class<Object>) HomeCompanyItemBean.class);
                                    Intrinsics.checkNotNullExpressionValue(fromJson, "gson.fromJson<HomeCompan…                        )");
                                    iExtendCallback.success((HomeCompanyItemBean) fromJson);
                                    return;
                                }
                                iExtendCallback.onFail(new ExtendErrorModel(responseCode, response + ""));
                            }
                        });
                    }
                });
            } catch (RemoteException e) {
                e.printStackTrace();
                Log.d(TAG, "requestAddPass: exception=");
                this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$_wDEZkqejzE2JGYnaZbl9CqMcTs
                    @Override // java.lang.Runnable
                    public final void run() {
                        ExtendApi.setCompanyAddressResult$lambda$1(IExtendCallback.this);
                    }
                });
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setCompanyAddressResult$lambda$1(IExtendCallback callback) {
        Intrinsics.checkNotNullParameter(callback, "$callback");
        callback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public void navigationPlanningRoute(LocationDetailModel info, final IExtendCallback<LocationDetail> callback) {
        String str = TAG;
        Log.d(str, "======================预约导航 导航规划路线========================1.0.31");
        if (callback == null) {
            Log.d(str, "预约导航 导航规划路线:callback= null");
            return;
        }
        if (!this.isBind) {
            Log.d(str, "======================预约导航 导航规划路线 服务未启动========================1");
            callback.onFail(new ExtendErrorModel(-1, "预约导航 导航规划路线 服务未启动"));
            return;
        }
        try {
            Log.d(str, "======================预约导航 导航规划路线========================1");
            String json = this.gson.toJson(info);
            Intrinsics.checkNotNullExpressionValue(json, "gson.toJson(info)");
            if (!TextUtils.isEmpty(json)) {
                ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
                Intrinsics.checkNotNull(extendServiceInterface);
                extendServiceInterface.navigationPlanningRoute(json, new ExtendApi$navigationPlanningRoute$1(this, callback));
            } else {
                Log.d(str, "======================预约导航 导航规划路线========================null");
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "======================预约导航 导航规划路线========================2");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$2aVXog2AHb6iLiDob_tJ9xh2P3U
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.navigationPlanningRoute$lambda$2(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void navigationPlanningRoute$lambda$2(IExtendCallback iExtendCallback) {
        Log.d(TAG, "======================预约导航 导航规划路线========================3");
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public void carltdCheckBindRequest(String userCenterId, final IExtendCallback<CarltdRequestInfoModel> callback) {
        String str = TAG;
        Log.d(str, "======================车企账号绑定检查请求========================1.0.31");
        if (callback == null) {
            Log.d(str, "车企账号绑定检查请求:callback= null");
            return;
        }
        if (!this.isBind) {
            Log.d(str, "车企账号绑定检查请求:服务没有绑定");
            callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
            return;
        }
        Log.d(str, "======================车企账号绑定检查请求========================1");
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.carltdCheckBindRequest(userCenterId, new ExtendApi$carltdCheckBindRequest$1(this, callback));
            }
        } catch (RemoteException e) {
            String str2 = TAG;
            Log.d(str2, "======================车企账号绑定检查请求========================2");
            e.printStackTrace();
            Log.d(str2, "requestAddPass: exception=");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$ELQB3Uyc4jjKS27IfR6zTZK3JME
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.carltdCheckBindRequest$lambda$3(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void carltdCheckBindRequest$lambda$3(IExtendCallback iExtendCallback) {
        Log.d(TAG, "======================车企账号绑定检查请求========================3");
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public void carltdUnBindRequest(String userCenterId, final IExtendCallback<CarltdRequestInfoModel> callback) {
        String str = TAG;
        Log.d(str, "======================解除账号绑定========================1.0.31");
        if (this.isBind) {
            if (callback == null) {
                Log.d(str, "车企账号解除账号绑定请求:callback= null");
                return;
            }
            try {
                ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
                if (extendServiceInterface != null) {
                    extendServiceInterface.carltdUnBindRequest(userCenterId, new ExtendApi$carltdUnBindRequest$1(this, callback));
                }
            } catch (RemoteException e) {
                e.printStackTrace();
                Log.d(TAG, "requestAddPass: exception=");
                this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$GrilVj59MAXTGjU6DVt44GlDRSA
                    @Override // java.lang.Runnable
                    public final void run() {
                        ExtendApi.carltdUnBindRequest$lambda$4(IExtendCallback.this);
                    }
                });
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void carltdUnBindRequest$lambda$4(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public void carltdBindRequest(String userCenterId, final IExtendCallback<CarltdRequestInfoModel> callback) {
        String str = TAG;
        Log.d(str, "绑定：" + userCenterId);
        Log.d(str, "======================申请账号绑定========================1.0.31");
        if (this.isBind) {
            if (callback == null) {
                Log.d(str, "车企账号申请账号绑定请求:callback= null");
                return;
            }
            try {
                ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
                if (extendServiceInterface != null) {
                    extendServiceInterface.carltdBindRequest(userCenterId, new ExtendApi$carltdBindRequest$1(this, callback));
                }
            } catch (RemoteException e) {
                e.printStackTrace();
                Log.d(TAG, "requestAddPass: exception=");
                this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$8dIyYN5BKAGy16vDq1oqqlQjPVY
                    @Override // java.lang.Runnable
                    public final void run() {
                        ExtendApi.carltdBindRequest$lambda$5(IExtendCallback.this);
                    }
                });
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void carltdBindRequest$lambda$5(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public void startMXnaviAppResult(final IExtendCallback<CarltdLoginResultBean> callback) {
        String str = TAG;
        Log.d(str, "======================拉起导航app========================01.0.31");
        if (callback == null) {
            Log.d(str, "拉起导航app:callback= null");
            return;
        }
        if (!this.isBind) {
            Log.d(str, "拉起导航app:拉起导航app 服务未启动l");
            callback.onFail(new ExtendErrorModel(-1, "拉起导航app 服务未启动"));
            return;
        }
        try {
            Log.d(str, "======================拉起导航app========================1");
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.startMXnaviApp(new ExtendApi$startMXnaviAppResult$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            String str2 = TAG;
            Log.d(str2, "======================拉起导航app========================2");
            Log.d(str2, "requestAddPass: exception=");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$Ro6qzG3s85Xr2ZX3CyWZY2xIWc8
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.startMXnaviAppResult$lambda$6(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void startMXnaviAppResult$lambda$6(IExtendCallback iExtendCallback) {
        Log.d(TAG, "======================拉起导航app========================3");
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    public final int getNaviStateResultcode() {
        return this.naviStateResultcode;
    }

    public final void setNaviStateResultcode(int i) {
        this.naviStateResultcode = i;
    }

    @Override // com.example.mxextend.IExtendApi
    public int getNaviState() {
        if (!this.isBind) {
            return -1;
        }
        int i = -2;
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                i = extendServiceInterface.getNaviState();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        if (i != this.naviStateResultcode) {
            Log.d(TAG, "getNaviState: resultcode=" + i);
        }
        this.naviStateResultcode = i;
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public int showMyLocation(int type, int operaType, IExtendCallback<LocationInfo> callback) {
        ExtendServiceInterface extendServiceInterface;
        if (callback == null) {
            Log.d(TAG, "showMyLocation:callback= null");
            return 3;
        }
        try {
        } catch (RemoteException e) {
            e.printStackTrace();
            String message = e.getMessage();
            Intrinsics.checkNotNull(message);
            ExtendErrorModel extendErrorModel = new ExtendErrorModel(-2, message);
            Log.d(TAG, "showMyLocation: exception");
            callback.onFail(extendErrorModel);
        }
        if (this.isBind && (extendServiceInterface = this.extendServiceInterface) != null) {
            if (extendServiceInterface != null) {
                extendServiceInterface.getCurrentLocationWithOpera(type, operaType, new ExtendApi$showMyLocation$1(this, callback));
            }
            return 3;
        }
        callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
        return 3;
    }

    @Override // com.example.mxextend.IExtendApi
    public int getNaviType() {
        try {
            if (!this.isBind) {
                Log.d(TAG, "getNaviType : unbind service");
                return 36;
            }
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            ResponseData naviType = extendServiceInterface != null ? extendServiceInterface.getNaviType() : null;
            Intrinsics.checkNotNull(naviType);
            Log.d(TAG, "getNaviType : responseCode = " + naviType.getResponseCode() + " response = " + naviType.getData());
            if (naviType.getResponseCode() != 10000) {
                return 36;
            }
            String data = naviType.getData();
            Intrinsics.checkNotNull(data);
            return Integer.parseInt(data);
        } catch (RemoteException e) {
            e.printStackTrace();
            return 36;
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void addExtendListener(IExtendListener listener) {
        if (listener == null || this.extendListeners.contains(listener)) {
            return;
        }
        this.extendListeners.add(listener);
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeExtendListener(IExtendListener listener) {
        if (listener == null) {
            return;
        }
        this.extendListeners.remove(listener);
    }

    @Override // com.example.mxextend.IExtendApi
    public void showInstrumentProjection(int screenType, ViewGroup view) {
        String str = TAG;
        Log.d(str, "=====================仪表投屏======================= screenType:" + screenType);
        if (this.isBind) {
            SurfaceMapConfig surfaceMapConfig = new SurfaceMapConfig();
            Intrinsics.checkNotNull(view);
            surfaceMapConfig.setMapWidth(view.getWidth());
            surfaceMapConfig.setMapHeight(view.getHeight());
            if (screenType == 0) {
                if (this.displaySurfaceView == null) {
                    Log.d(str, "initDisplaySurfaceView");
                    initDisplaySurfaceView(this.mShowMode);
                }
                DisplaySurfaceView displaySurfaceView = this.displaySurfaceView;
                Intrinsics.checkNotNull(displaySurfaceView);
                displaySurfaceView.extendServiceInterface = this.extendServiceInterface;
                if (!displaySurfaceView.isBind) {
                    Log.d(str, "showInstrumentProjection: displaySurfaceView is Bind");
                    displaySurfaceView.onBindSurfaceViewListener();
                }
                Log.d("DisplaySurfaceView", "showInstrumentProjection: " + displaySurfaceView);
                removeSelfFromParent(displaySurfaceView);
                displaySurfaceView.setSurfaceMapConfig(surfaceMapConfig);
                view.addView(this.displaySurfaceView, new ViewGroup.LayoutParams(-1, -1));
            }
        }
    }

    private final void removeSelfFromParent(View child) {
        if (child == null || !(child.getParent() instanceof ViewGroup)) {
            return;
        }
        ViewParent parent = child.getParent();
        Intrinsics.checkNotNull(parent, "null cannot be cast to non-null type android.view.ViewGroup");
        ViewGroup viewGroup = (ViewGroup) parent;
        Log.d(TAG, "removeSelfFromParent: parent:" + viewGroup);
        viewGroup.removeView(child);
    }

    private final void initDisplaySurfaceView(int showMode) {
        SurfaceMapConfig surfaceMapConfig = new SurfaceMapConfig();
        surfaceMapConfig.setMapWidth(1920);
        surfaceMapConfig.setMapHeight(720);
        this.displaySurfaceView = new DisplaySurfaceView(this.context, showMode, surfaceMapConfig, this.extendServiceInterface);
        Log.d(TAG, "initDisplaySurfaceView: displaySurfaceView is Bind");
        DisplaySurfaceView displaySurfaceView = this.displaySurfaceView;
        if (displaySurfaceView != null) {
            displaySurfaceView.onBindSurfaceViewListener();
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void stopInstrumentProjection(int screenType) {
        Log.d(TAG, "=====================仪表结束投屏=======================");
        if (this.isBind) {
            try {
                ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
                if (extendServiceInterface != null) {
                    extendServiceInterface.stopInstrumentProjection(screenType);
                }
                DisplaySurfaceView displaySurfaceView = this.displaySurfaceView;
                if (displaySurfaceView != null) {
                    displaySurfaceView.dissmissDifferentDislayGuideView();
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void transStsRequest() {
        String str = TAG;
        Log.d(str, "transStsRequest: ");
        if (!this.isBind) {
            Log.d(str, "服务未启动");
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.transStsRequest();
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void getMXnaviAppLoginResult(final IExtendCallback<CarltdLoginResultBean> callback) {
        String str = TAG;
        Log.d(str, "======================获取登录信息========================1.0.31");
        if (this.isBind) {
            if (callback == null) {
                Log.d(str, "获取登录信息:callback= null");
                return;
            }
            try {
                ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
                if (extendServiceInterface != null) {
                    extendServiceInterface.getMXnaviAppLoginResult(new ExtendApi$getMXnaviAppLoginResult$1(this, callback));
                }
            } catch (Exception e) {
                Log.d(TAG, "requestAddPass: exception= " + e);
                e.printStackTrace();
                this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$EgMRbCufnICbj_xAHKvD6Vlha80
                    @Override // java.lang.Runnable
                    public final void run() {
                        ExtendApi.getMXnaviAppLoginResult$lambda$8(IExtendCallback.this);
                    }
                });
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void getMXnaviAppLoginResult$lambda$8(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public void searchPoi(RequestSearchData requestSearchData, final IExtendCallback<SearchResultModel> callback) {
        String str = TAG;
        Log.d(str, "==================searchNearByPoi================1.0.31");
        if (callback == null) {
            Log.d(str, "searchPoi:callback= null");
            return;
        }
        try {
            if (!this.isBind) {
                callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
                return;
            }
            if (requestSearchData == null) {
                callback.onFail(new ExtendErrorModel(-10003, "参数有误，RequestSearchData 不能为 null"));
                return;
            }
            if (TextUtils.isEmpty(requestSearchData.getSearchKey())) {
                callback.onFail(new ExtendErrorModel(-10003, "参数有误，检索关键字不能为空"));
                return;
            }
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.searchNearByPoi(requestSearchData.getNearbyKey(), requestSearchData.getSearchKey(), requestSearchData.isNeedShow(), requestSearchData.getIntentionType(), requestSearchData.getViaIndex(), new ExtendApi$searchPoi$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "searchNearByPoi: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$3MEdjZC5oqPrbHErr3OExHDz-iU
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.searchPoi$lambda$9(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void searchPoi$lambda$9(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public void getAddressByCoordinate(double lon, double lat, IExtendCallback<AddressModel> callback) {
        String str = TAG;
        Log.d(str, "==================getAddressByLocation================1.0.31");
        Log.d(str, "getAddressByCoordinate: lon = " + lon + " , lat = " + lat + " , isBind = " + this.isBind);
        if (callback == null) {
            Log.d(str, "getAddressByCoordinate: callback = null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
            return;
        }
        ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
        if (extendServiceInterface != null) {
            extendServiceInterface.getAddressByCoordinate(lon, lat, new ExtendApi$getAddressByCoordinate$1(this, callback));
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void naviToPoi(boolean isRefresh, String poiName, final IExtendCallback<ExtendBaseModel> callback) {
        String str = TAG;
        Log.d(str, "================naviToPoi=============1.0.31");
        if (callback == null) {
            Log.d(str, "naviToPoi:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未绑定"));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Intrinsics.checkNotNull(extendServiceInterface);
            extendServiceInterface.naviToPoi(isRefresh, poiName, new ExtendApi$naviToPoi$1(this, callback));
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "naviToPoi: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$p9LANwqGTzkjE0NGkwCKvIFySB8
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.naviToPoi$lambda$10(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void naviToPoi$lambda$10(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生错误"));
    }

    @Override // com.example.mxextend.IExtendApi
    public void navigation(LocationInfo info, boolean isShowMutilPage, final IExtendCallback<ExtendBaseModel> callback) {
        String str = TAG;
        Log.d(str, "===============navigation====================1.0.31");
        if (info == null || info.getLatitude() <= 0.0d || info.getLongitude() <= 0.0d) {
            if (callback != null) {
                callback.onFail(new ExtendErrorModel(-10003, "服务未绑定"));
                return;
            }
            return;
        }
        if (callback == null) {
            Log.d(str, "naviToPoi:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未绑定"));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.navigation(this.gson.toJson(info), isShowMutilPage, new ExtendApi$navigation$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "naviToPoi: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$x-vDT2SCrmGGfcMz-Tqudz01r5c
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.navigation$lambda$11(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void navigation$lambda$11(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生错误"));
    }

    @Override // com.example.mxextend.IExtendApi
    public void naviToPoi(boolean isRefres, LocationInfo info, IExtendCallback<ExtendBaseModel> callback) {
        String str = TAG;
        Log.d(str, "================naviToPoinaviToPoi=============1.0.31");
        if (info == null || info.getLatitude() <= 0.0d || info.getLongitude() <= 0.0d) {
            Intrinsics.checkNotNull(callback);
            callback.onFail(new ExtendErrorModel(-10003, "服务未绑定"));
        } else {
            Log.d(str, "naviToPoi: info ==" + info);
            naviToPoi(isRefres, this.gson.toJson(info), callback);
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void goToNearbyGasStation(String nearbyName, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "goToNearbyGasStation:callback= null");
            return;
        }
        Log.d(TAG, "goToNearbyGasStation");
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.goToNearbyGasStation(nearbyName, new IServiceCallBack.Stub() { // from class: com.example.mxextend.ExtendApi$goToNearbyGasStation$1
                    @Override // com.mxnavi.busines.IServiceCallBack
                    public void onResponse(int responseCode, String response) throws RemoteException {
                        String str;
                        Intrinsics.checkNotNullParameter(response, "response");
                        str = ExtendApi.TAG;
                        Log.d(str, "goToNearbyGasStation:responseCode=" + responseCode + "   response=" + response);
                        if (responseCode == 10000) {
                            callback.success(new ExtendBaseModel());
                        } else if (responseCode != 10012) {
                            if (responseCode == 10016) {
                                callback.gotoVoiceResult();
                            } else {
                                callback.onFail(new ExtendErrorModel(responseCode, ""));
                            }
                        }
                    }
                });
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "goToNearbyGasStation:exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$AwhGZxBWZ0tcr63oDKbGWVkGfW4
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.goToNearbyGasStation$lambda$12(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void goToNearbyGasStation$lambda$12(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public void requestPoiData(String searchKey, IExtendCallback<SearchResultModel> callback) {
        String str = TAG;
        Log.d(str, "=========导航去某地==============1.0.31");
        if (callback == null) {
            Log.d(str, "requestPoiData:callback= null");
            return;
        }
        RequestSearchData requestSearchData = new RequestSearchData(null, null, null, 0, false, 0, false, 0.0f, 0, 0, 1023, null);
        requestSearchData.setSearchKey(searchKey);
        searchPoi(requestSearchData, callback);
    }

    @Override // com.example.mxextend.IExtendApi
    public void cancelNavigation(final IExtendCallback<ExtendBaseModel> callback) {
        String str = TAG;
        Log.d(str, "======================cancelNavigation取消导航========================1.0.31");
        if (callback == null) {
            Log.d(str, "取消导航:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "取消导航 服务未启动"));
            return;
        }
        try {
            Log.d(str, "======================取消导航========================1");
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.cancelNavigation(new ExtendApi$cancelNavigation$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "======================取消导航========================2");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$lTBpFJNTOSIILSie0TzfqbjAIGc
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.cancelNavigation$lambda$13(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void cancelNavigation$lambda$13(IExtendCallback iExtendCallback) {
        Log.d(TAG, "======================取消导航 1========================3");
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int mapOpera(int actionType, int operaType, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "mapOpera:callback= null");
            return 1;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return 1;
        }
        try {
            Log.d(TAG, "mapOpera:=============mapOpera=======================3.6.5");
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.mapOpera(actionType, operaType, new IServiceCallBack.Stub() { // from class: com.example.mxextend.ExtendApi$mapOpera$1
                    @Override // com.mxnavi.busines.IServiceCallBack
                    public void onResponse(int responseCode, String response) {
                        String str;
                        str = ExtendApi.TAG;
                        Log.d(str, "mapOpera: resultcode=" + responseCode);
                        if (responseCode == 10000) {
                            callback.success(new ExtendBaseModel());
                        } else {
                            callback.onFail(new ExtendErrorModel(responseCode, ""));
                        }
                    }
                });
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            callback.onFail(new ExtendErrorModel(-2, ""));
        }
        return 1;
    }

    @Override // com.example.mxextend.IExtendApi
    public int showTraffic(ShowTrafficModel showTrafficModel, IExtendCallback<TrafficInfoModel> callback) {
        if (callback == null) {
            Log.d(TAG, "showTraffic:callback= null");
            return 4;
        }
        Intrinsics.checkNotNull(showTrafficModel);
        searchTraffic(1, showTrafficModel.getTrafficText(), callback);
        return 4;
    }

    @Override // com.example.mxextend.IExtendApi
    public int showFrontTraffic(ShowFrontTrafficModel showTrafficModel, IExtendCallback<TrafficInfoModel> callback) {
        if (callback == null) {
            Log.d(TAG, "showFrontTraffic:callback= null");
            return 8;
        }
        searchTraffic(0, "", callback);
        return 8;
    }

    private final void searchTraffic(int type, String keyWord, final IExtendCallback<TrafficInfoModel> callback) {
        if (callback == null) {
            Log.d(TAG, "searchTraffic:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.searchTraffic(type, keyWord, new ExtendApi$searchTraffic$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$EpLh7gNLWsupufbA0z1It5d1XnA
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.searchTraffic$lambda$14(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void searchTraffic$lambda$14(IExtendCallback iExtendCallback) {
        Log.d(TAG, "searchTraffic: exception");
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生错误"));
    }

    @Override // com.example.mxextend.IExtendApi
    public int cancelNavi() {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.cancelNavi()) : null;
            Intrinsics.checkNotNull(valueOf);
            i = valueOf.intValue();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "cancelNavi: resultcode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public void addAccountChangedListener(IAccountListener listener) {
        if (listener == null || this.accountListeners.contains(listener)) {
            return;
        }
        this.accountListeners.add(listener);
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeAccountChangedListener(IAccountListener listener) {
        if (listener != null) {
            this.accountListeners.remove(listener);
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public int specialPoiNavi(int destType, int directNavi, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "specialPoiNavi:callback= null");
            return 11;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return 11;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.goHomeOrCompany(destType, directNavi)) : null;
            Intrinsics.checkNotNull(valueOf);
            final int intValue = valueOf.intValue();
            Log.d(TAG, "specialPoiNavi: resultcode=" + intValue);
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$eSlLPjVD9HhdayhQT1M6TTTPiL4
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.specialPoiNavi$lambda$15(intValue, callback);
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "specialPoiNavi: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$zg7DebM4MhEGr-5XQQY970K9uVU
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.specialPoiNavi$lambda$16(IExtendCallback.this);
                }
            });
        }
        return 11;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void specialPoiNavi$lambda$15(int i, IExtendCallback iExtendCallback) {
        if (i == 10000) {
            iExtendCallback.success(new ExtendBaseModel());
        } else {
            iExtendCallback.onFail(new ExtendErrorModel(i, ""));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void specialPoiNavi$lambda$16(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int specialPoiNavi(int destType, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "specialPoiNavi2:callback= null");
            return 11;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return 11;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Intrinsics.checkNotNull(extendServiceInterface);
            extendServiceInterface.goHomeOrCompany2(destType, new IServiceCallBack.Stub() { // from class: com.example.mxextend.ExtendApi$specialPoiNavi$3
                @Override // com.mxnavi.busines.IServiceCallBack
                public void onResponse(int responseCode, String response) throws RemoteException {
                    String str;
                    Intrinsics.checkNotNullParameter(response, "response");
                    str = ExtendApi.TAG;
                    Log.d(str, "specialPoiNavi2:responseCode=" + responseCode + "   response=" + response);
                    if (responseCode == 10000) {
                        callback.success(new ExtendBaseModel());
                    } else {
                        callback.onFail(new ExtendErrorModel(responseCode, ""));
                    }
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "specialPoiNavi: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$i8epPa3lsX24Qeq094Ty46zSpv4
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.specialPoiNavi$lambda$17(IExtendCallback.this);
                }
            });
        }
        return 11;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void specialPoiNavi$lambda$17(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int setSpecialPoi(int type, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "setSpecialPoi:callback= null");
            return 16;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return 16;
        }
        String str = TAG;
        Log.d(str, "setSpecialPoi: type=" + type);
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.collectCurrentPos(type)) : null;
            Intrinsics.checkNotNull(valueOf);
            final int intValue = valueOf.intValue();
            Log.d(str, "setSpecialPoi: resultcode=" + intValue);
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$08EnaLaqgGR_ZyLz1U2OztdQun4
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.setSpecialPoi$lambda$18(intValue, callback);
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "setSpecialPoi: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$2NJoxykdtL2O8zF48EMmJwUirW4
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.setSpecialPoi$lambda$19(IExtendCallback.this);
                }
            });
        }
        return 16;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setSpecialPoi$lambda$18(int i, IExtendCallback iExtendCallback) {
        if (i == 10000) {
            iExtendCallback.success(new ExtendBaseModel());
        } else {
            iExtendCallback.onFail(new ExtendErrorModel(i, ""));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setSpecialPoi$lambda$19(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int collectByPoi(int type, LocationInfo info, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "collectByPoi:callback= null");
            return 20;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return 20;
        }
        Log.d(TAG, "collectByPoi: type=" + type + "  info=" + info);
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.collectByPoi(type, this.gson.toJson(info), new ExtendApi$collectByPoi$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "collectByPoi: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$jx8KNqpHKw_CtBI08Sf7X91tCTY
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.collectByPoi$lambda$20(IExtendCallback.this);
                }
            });
        }
        return 20;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void collectByPoi$lambda$20(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int getSpeakMode() {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Intrinsics.checkNotNull(extendServiceInterface);
            i = extendServiceInterface.getSpeakMode();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "getSpeakMode: resultcode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public int setSpeakMode(int speakMode) {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Intrinsics.checkNotNull(extendServiceInterface);
            i = extendServiceInterface.setSpeakMode(speakMode);
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "setSpeakMode: resultcode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public int getScaleLevel() {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.getScaleLevel()) : null;
            Intrinsics.checkNotNull(valueOf);
            i = valueOf.intValue();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "getScaleLevel: resultcode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public int lookOverView() {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.lookOverView()) : null;
            Intrinsics.checkNotNull(valueOf);
            i = valueOf.intValue();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "lookOverView: resultcode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public int goTeamTrip() {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.goTeamTrip()) : null;
            Intrinsics.checkNotNull(valueOf);
            i = valueOf.intValue();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "goTeamTrip: resultcode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public int goSetting(final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "goSetting:callback= null");
            return 18;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return 18;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.goSetting()) : null;
            Intrinsics.checkNotNull(valueOf);
            final int intValue = valueOf.intValue();
            Log.d(TAG, "goSetting: resultcode=" + intValue);
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$2-7WakkNQXVaLDYEOVuGrEEMjvI
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.goSetting$lambda$21(intValue, callback);
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$RJTFyQK3vi2jP3mn23eiLFuOMHg
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.goSetting$lambda$22(IExtendCallback.this);
                }
            });
        }
        return 18;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void goSetting$lambda$21(int i, IExtendCallback iExtendCallback) {
        if (i == 10000) {
            iExtendCallback.success(new ExtendBaseModel());
        } else {
            iExtendCallback.onFail(new ExtendErrorModel(i, ""));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void goSetting$lambda$22(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
        Log.d(TAG, "goSetting:exception");
    }

    @Override // com.example.mxextend.IExtendApi
    public int changePreference(int preferenceId) {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.changePreference(preferenceId)) : null;
            Intrinsics.checkNotNull(valueOf);
            i = valueOf.intValue();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "changePreference: resultcode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public int changeNaviRoutePrefer(int strategy, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "changeNaviRoutePrefer:callback= null");
            return 14;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
            return 14;
        }
        try {
            String str = TAG;
            Log.d(str, "changeNaviRoutePrefer:req strategy=" + strategy);
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.changePreference(strategy)) : null;
            Intrinsics.checkNotNull(valueOf);
            final int intValue = valueOf.intValue();
            Log.d(str, "changeNaviRoutePrefer: resultcode=" + intValue);
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$w6cNHQrygmXGU99R1f9tHFpseVI
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.changeNaviRoutePrefer$lambda$23(intValue, callback);
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$A1T-CR88F-E93uBmrqA70a61Lgg
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.changeNaviRoutePrefer$lambda$24(IExtendCallback.this);
                }
            });
            Log.d(TAG, "changeNaviRoutePrefer: exception");
        }
        return 14;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void changeNaviRoutePrefer$lambda$23(int i, IExtendCallback iExtendCallback) {
        if (i == 10000) {
            iExtendCallback.success(new ExtendBaseModel());
        } else {
            iExtendCallback.onFail(new ExtendErrorModel(i, ""));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void changeNaviRoutePrefer$lambda$24(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public void getDestInfo(IExtendCallback<LocationInfo> callback) {
        if (callback == null) {
            Log.d(TAG, "getDestInfo:callback= null");
            return;
        }
        try {
            if (!this.isBind) {
                callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
                return;
            }
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Intrinsics.checkNotNull(extendServiceInterface);
            ResponseData destInfo = extendServiceInterface.getDestInfo();
            Log.d(TAG, "getDestInfo:" + this.gson.toJson(destInfo));
            if (destInfo.getResponseCode() == 10000) {
                Object fromJson = this.gson.fromJson(destInfo.getData(), (Class<Object>) LocationInfo.class);
                Intrinsics.checkNotNull(fromJson, "null cannot be cast to non-null type com.example.mxextend.entity.LocationInfo");
                callback.success((LocationInfo) fromJson);
            } else {
                int responseCode = destInfo.getResponseCode();
                String data = destInfo.getData();
                Intrinsics.checkNotNull(data);
                callback.onFail(new ExtendErrorModel(responseCode, data));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            String message = e.getMessage();
            Intrinsics.checkNotNull(message);
            callback.onFail(new ExtendErrorModel(-2, message));
            Log.d(TAG, "getDestInfo: getDestInfo");
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public int getRemainDistance() {
        try {
            if (!this.isBind) {
                return -1;
            }
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            ResponseData remainDistance = extendServiceInterface != null ? extendServiceInterface.getRemainDistance() : null;
            Intrinsics.checkNotNull(remainDistance);
            Log.d(TAG, "getRemainDistance:" + this.gson.toJson(remainDistance));
            if (remainDistance.getResponseCode() == 10000) {
                Integer valueOf = Integer.valueOf(remainDistance.getData());
                Intrinsics.checkNotNullExpressionValue(valueOf, "{\n                Intege…tInfo.data)\n            }");
                return valueOf.intValue();
            }
            return remainDistance.getResponseCode();
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "getRemainDistance: exception");
            return -2;
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public int getRemainTime() {
        try {
            if (!this.isBind) {
                return -1;
            }
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            ResponseData remainTime = extendServiceInterface != null ? extendServiceInterface.getRemainTime() : null;
            Intrinsics.checkNotNull(remainTime);
            Log.d(TAG, "getRemainTime:" + this.gson.toJson(remainTime));
            if (remainTime.getResponseCode() == 10000) {
                Integer valueOf = Integer.valueOf(remainTime.getData());
                Intrinsics.checkNotNullExpressionValue(valueOf, "{\n                Intege…tInfo.data)\n            }");
                return valueOf.intValue();
            }
            return remainTime.getResponseCode();
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "getRemainTime: exception");
            return -2;
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public int searchNearByPoi(String nearbyKey, String searchKey, IExtendCallback<SearchResultModel> callback) {
        if (callback == null) {
            Log.d(TAG, "searchNearByPoi:callback= null");
            return 7;
        }
        RequestSearchData requestSearchData = new RequestSearchData(null, null, null, 0, false, 0, false, 0.0f, 0, 0, 1023, null);
        requestSearchData.setNearbyKey(nearbyKey);
        requestSearchData.setSearchKey(searchKey);
        searchPoi(requestSearchData, callback);
        return 7;
    }

    @Override // com.example.mxextend.IExtendApi
    public int keywordSearch(String searchKey, IExtendCallback<SearchResultModel> callback) {
        if (callback == null) {
            Log.d(TAG, "keywordSearch:callback= null");
            return 5;
        }
        RequestSearchData requestSearchData = new RequestSearchData(null, null, null, 0, false, 0, false, 0.0f, 0, 0, 1023, null);
        requestSearchData.setSearchKey(searchKey);
        searchPoi(requestSearchData, callback);
        return 5;
    }

    @Override // com.example.mxextend.IExtendApi
    public int keywordSearch(String searchKey, int searchAction, IExtendCallback<SearchResultModel> callback) {
        if (callback == null) {
            Log.d(TAG, "keywordSearch:callback= null");
            return 5;
        }
        RequestSearchData requestSearchData = new RequestSearchData(null, null, null, 0, false, 0, false, 0.0f, 0, 0, 1023, null);
        requestSearchData.setSearchKey(searchKey);
        requestSearchData.setSearchAction(searchAction);
        searchPoi(requestSearchData, callback);
        return 5;
    }

    @Override // com.example.mxextend.IExtendApi
    public int aroundSearch(String nearbyKey, String searchKey, int searchAction, float distance, IExtendCallback<SearchResultModel> callback) {
        if (callback == null) {
            Log.d(TAG, "aroundSearch:callback= null");
            return 6;
        }
        RequestSearchData requestSearchData = new RequestSearchData(null, null, null, 0, false, 0, false, 0.0f, 0, 0, 1023, null);
        requestSearchData.setNearbyKey(nearbyKey);
        requestSearchData.setSearchKey(searchKey);
        requestSearchData.setNearBySearch(true);
        requestSearchData.setNeedShow(true);
        requestSearchData.setSearchAction(searchAction);
        requestSearchData.setDistance(distance);
        searchPoi(requestSearchData, callback);
        return 6;
    }

    @Override // com.example.mxextend.IExtendApi
    public void searchAlongRoute(String searchKey, final IExtendCallback<SearchResultModel> callback) {
        if (callback == null) {
            Log.d(TAG, "searchAlongRoute:callback= null");
            return;
        }
        try {
            if (!this.isBind) {
                callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
                return;
            }
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.searchAlongRoute(searchKey, true, new ExtendApi$searchAlongRoute$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "searchAlongRoute: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$pquT3opnDkalQlY96exjcRJrdzg
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.searchAlongRoute$lambda$25(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void searchAlongRoute$lambda$25(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public void requestAlongRouteData(String searchKey, final IExtendCallback<SearchResultModel> callback) {
        if (callback == null) {
            Log.d(TAG, "requestAlongRouteData:callback= null");
            return;
        }
        try {
            if (!this.isBind) {
                callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
                return;
            }
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.searchAlongRoute(searchKey, false, new ExtendApi$requestAlongRouteData$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "requestAlongRouteData: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$M6oHhz7rOm-FWq2lwnBmU2qwSa0
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.requestAlongRouteData$lambda$26(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void requestAlongRouteData$lambda$26(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public int accountOpera(int actionType) {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.accountOpera(actionType)) : null;
            Intrinsics.checkNotNull(valueOf);
            i = valueOf.intValue();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "accountopera:action=" + actionType + "resp= " + i + "");
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public int naviOpera(int actionType, int operaType, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "naviOpera:callback= null");
            return 13;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return 13;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.naviOpera(actionType, operaType, new IServiceCallBack.Stub() { // from class: com.example.mxextend.ExtendApi$naviOpera$1
                    @Override // com.mxnavi.busines.IServiceCallBack
                    public void onResponse(int responseCode, String response) throws RemoteException {
                        String str;
                        Intrinsics.checkNotNullParameter(response, "response");
                        str = ExtendApi.TAG;
                        Log.d(str, "naviOpera: resultcode=" + responseCode);
                        if (responseCode == 10000) {
                            callback.success(new ExtendBaseModel());
                        } else {
                            callback.onFail(new ExtendErrorModel(responseCode, ""));
                        }
                    }
                });
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "naviOpera: exception");
            callback.onFail(new ExtendErrorModel(-2, ""));
        }
        return 13;
    }

    @Override // com.example.mxextend.IExtendApi
    public int getAccountStatus() {
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            ResponseData accountStatus = extendServiceInterface != null ? extendServiceInterface.getAccountStatus() : null;
            Intrinsics.checkNotNull(accountStatus);
            Log.d(TAG, "getAccountStatus:" + this.gson.toJson(accountStatus));
            if (accountStatus.getResponseCode() != 10000) {
                return -2;
            }
            return !TextUtils.isEmpty(accountStatus.getData()) ? 1 : 0;
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "getAccountStatus: exception");
            return -2;
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public int sendWxPosition(double longitude, double latitude) {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            PoiBean poiBean = new PoiBean("", "", longitude, latitude);
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.sendWxPosition(this.gson.toJson(poiBean))) : null;
            Intrinsics.checkNotNull(valueOf);
            i = valueOf.intValue();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "sendWxPosition:responseCode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public void getCityInfo(final IExtendCallback<CityInfo> callback) {
        if (callback == null) {
            Log.d(TAG, "getCityInfo:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            final ResponseData cityInfo = extendServiceInterface != null ? extendServiceInterface.getCityInfo() : null;
            Log.d(TAG, "getCityInfo:" + this.gson.toJson(cityInfo));
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$xQxq0x7LqjWDvUNM72TbL9Nb7gc
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.getCityInfo$lambda$27(ResponseData.this, this, callback);
                }
            });
        } catch (RemoteException e) {
            Log.d(TAG, "getCityInfo: exception");
            e.printStackTrace();
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$tH3PBIKg4obKrqDlXhPSMBMtdwM
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.getCityInfo$lambda$28(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void getCityInfo$lambda$27(ResponseData responseData, ExtendApi this$0, final IExtendCallback iExtendCallback) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        boolean z = false;
        if (responseData != null && responseData.getResponseCode() == 10000) {
            z = true;
        }
        if (z) {
            final CityInfo cityInfo = (CityInfo) this$0.gson.fromJson(responseData.getData(), CityInfo.class);
            this$0.getAddressByCoordinate(cityInfo.getLongitude(), cityInfo.getLatitude(), new IExtendCallback<AddressModel>() { // from class: com.example.mxextend.ExtendApi$getCityInfo$1$1
                @Override // com.example.mxextend.listener.IExtendCallback
                public void success(AddressModel t) {
                    String str;
                    Intrinsics.checkNotNullParameter(t, "t");
                    str = ExtendApi.TAG;
                    Log.d(str, "getCityInfo: 城市获取成功，且已填充市区信息：" + t.getDistrict());
                    CityInfo.this.setDistrictName(t.getDistrict());
                    IExtendCallback<CityInfo> iExtendCallback2 = iExtendCallback;
                    CityInfo cityInfoBean = CityInfo.this;
                    Intrinsics.checkNotNullExpressionValue(cityInfoBean, "cityInfoBean");
                    iExtendCallback2.success(cityInfoBean);
                }

                @Override // com.example.mxextend.listener.IExtendCallback
                public void onFail(ExtendErrorModel errorModel) {
                    String str;
                    str = ExtendApi.TAG;
                    Log.d(str, "getCityInfo: 城市获取成功，但未填充市区信息");
                    IExtendCallback<CityInfo> iExtendCallback2 = iExtendCallback;
                    CityInfo cityInfoBean = CityInfo.this;
                    Intrinsics.checkNotNullExpressionValue(cityInfoBean, "cityInfoBean");
                    iExtendCallback2.success(cityInfoBean);
                }
            });
        } else {
            Log.d(TAG, "getCityInfo: 城市获取失败");
            Intrinsics.checkNotNull(responseData);
            iExtendCallback.onFail(new ExtendErrorModel(responseData.getResponseCode(), ""));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void getCityInfo$lambda$28(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public void calculateRoad(int i, final IExtendCallback<RouteResult> callback) {
        if (callback == null) {
            Log.d(TAG, "calculateRoad:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.calculateRoad(i, new ExtendApi$calculateRoad$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "calculateRoad: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$mmK8RCObg26q_ucKruM5IUmD7EE
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.calculateRoad$lambda$29(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void calculateRoad$lambda$29(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public int selectRoute(int selectId, IExtendCallback<ExtendBaseModel> callback) {
        Integer valueOf;
        Intrinsics.checkNotNullParameter(callback, "callback");
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return 12;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.selectRouteToNavi(selectId, new ExtendApi$selectRoute$result$1(this, callback))) : null;
            Log.d(TAG, "selectRoute: resultcode=" + valueOf);
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "selectRoute: exception");
            callback.onFail(new ExtendErrorModel(-2, ""));
        }
        if (valueOf != null && valueOf.intValue() == 10000) {
            callback.success(new ExtendBaseModel());
            return 12;
        }
        Intrinsics.checkNotNull(valueOf);
        callback.onFail(new ExtendErrorModel(valueOf.intValue(), ""));
        return 12;
    }

    @Override // com.example.mxextend.IExtendApi
    public void backToMap(int type, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "backToMap:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return;
        }
        try {
            Log.d(TAG, "=============backToMap==============3.6.5");
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.backToMap(type, new ExtendApi$backToMap$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "backToMap: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$x22qV_-qeEBJIEpRSa2F7A8vjC0
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.backToMap$lambda$30(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void backToMap$lambda$30(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int pageOprea(PageOpreaData data, final IExtendCallback<ExtendBaseModel> callback) {
        String str = TAG;
        Log.d(str, "pageOprea:" + data);
        if (callback == null) {
            Log.d(str, "pageOprea:callback= null");
            return 19;
        }
        if (data == null || data.getPageId() == 0) {
            callback.onFail(new ExtendErrorModel(-10003, ""));
            return 19;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return 19;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.pageOpera(data, new ExtendApi$pageOprea$responseCode$1(this, callback))) : null;
            Intrinsics.checkNotNull(valueOf);
            Log.d(str, "pageOprea: resultcode=" + valueOf.intValue());
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "pageOprea: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$72mGeetv74cM_B2p4JsUs4ole-E
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.pageOprea$lambda$31(IExtendCallback.this);
                }
            });
        }
        return 19;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void pageOprea$lambda$31(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int pageOprea(int pageId, int action, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "pageOprea:callback= null");
            return 19;
        }
        if (pageId == 0) {
            callback.onFail(new ExtendErrorModel(-10003, ""));
            return 19;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return 19;
        }
        try {
            PageOpreaData pageOpreaData = new PageOpreaData();
            pageOpreaData.setPageId(pageId);
            pageOpreaData.setAction(action);
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.pageOpera(pageOpreaData, new ExtendApi$pageOprea$responseCode$2(this, callback))) : null;
            Intrinsics.checkNotNull(valueOf);
            final int intValue = valueOf.intValue();
            Log.d(TAG, "pageOprea: resultcode=" + intValue);
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$v9G6eqSjajYD-o4KeL1DDApjk8s
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.pageOprea$lambda$32(intValue, callback);
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "pageOprea: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$iz9IoCIt4z-vI9l8tNqsQSRS7y8
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.pageOprea$lambda$33(IExtendCallback.this);
                }
            });
        }
        return 19;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void pageOprea$lambda$32(int i, IExtendCallback iExtendCallback) {
        if (i == 10000) {
            iExtendCallback.success(new ExtendBaseModel());
        } else if (i != 10110) {
            iExtendCallback.onFail(new ExtendErrorModel(i, ""));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void pageOprea$lambda$33(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int setVolumeMute(boolean isMute, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "setVolumeMute:callback= null");
            return 17;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return 17;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.setVolumeMute(isMute)) : null;
            Intrinsics.checkNotNull(valueOf);
            final int intValue = valueOf.intValue();
            Log.d(TAG, "setVolumeMute: resultcode=" + intValue);
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$1916dJdq-i93UscU54lrsKYGe70
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.setVolumeMute$lambda$34(intValue, callback);
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "setVolumeMute: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$alOVmsoLhEbysWCxlVKRaMQTJeI
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.setVolumeMute$lambda$35(IExtendCallback.this);
                }
            });
        }
        return 17;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setVolumeMute$lambda$34(int i, IExtendCallback iExtendCallback) {
        if (i == 10000) {
            iExtendCallback.success(new ExtendBaseModel());
        } else {
            iExtendCallback.onFail(new ExtendErrorModel(i, ""));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setVolumeMute$lambda$35(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int isVolumeMute() {
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.isVolumeMute()) : null;
            Intrinsics.checkNotNull(valueOf);
            int intValue = valueOf.intValue();
            Log.d(TAG, "isVolumeMute: resultcode=" + intValue);
            return intValue;
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "isVolumeMute: exception");
            return -2;
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public int requestRouteEx(RequestRouteExModel model, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "requestRouteEx:callback= null");
            return 9;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
            return 9;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.requestRouteEx(model, new ExtendApi$requestRouteEx$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "requestRouteEx: exception=");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$1a22TnSv_aaqzsSw7lDkAgfObN0
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.requestRouteEx$lambda$36(IExtendCallback.this);
                }
            });
        }
        return 9;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void requestRouteEx$lambda$36(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int requestAddPass(ModifyNaviViaModel model, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "requestAddPass:callback= null");
            return 10;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
            return 10;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.requestAddPass(model, new ExtendApi$requestAddPass$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "requestAddPass: exception=");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$rHBdx2uKBzM6LRur2yFxZUTKRiY
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.requestAddPass$lambda$37(IExtendCallback.this);
                }
            });
        }
        return 10;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void requestAddPass$lambda$37(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int requestGuideInfo(RequestGuideInfoModel requestGuideInfoModel, final IExtendCallback<GuideInfoModel> callback) {
        if (callback == null) {
            Log.d(TAG, "requestGuideInfo:callback= null");
            return 15;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
            return 15;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.requestGuideInfo(new ExtendApi$requestGuideInfo$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$Aqv5lO8L4oGRWYRpRdmd5RGjF1A
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.requestGuideInfo$lambda$38(IExtendCallback.this);
                }
            });
        }
        return 15;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void requestGuideInfo$lambda$38(IExtendCallback iExtendCallback) {
        Log.d(TAG, "requestGuideInfo: exception=");
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public void addLocationChangedListener(ILocationChangedListener locationChangedListener) {
        if (locationChangedListener == null || this.locationListeners.contains(locationChangedListener)) {
            return;
        }
        this.locationListeners.add(locationChangedListener);
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeLocationChangedListener(ILocationChangedListener locationChangedListener) {
        if (locationChangedListener != null) {
            this.locationListeners.remove(locationChangedListener);
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void addRouteDataListener(IRouteDataListener listener) {
        if (listener == null || this.routeDataListeners.contains(listener)) {
            return;
        }
        this.routeDataListeners.add(listener);
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeRouteDataListener(IRouteDataListener listener) {
        if (listener != null) {
            this.routeDataListeners.remove(listener);
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void addSearchDataListener(ISearchDataListener listener) {
        if (listener == null || this.searchDataListeners.contains(listener)) {
            return;
        }
        this.searchDataListeners.add(listener);
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeSearchDataListener(ISearchDataListener listener) {
        if (listener != null) {
            this.searchDataListeners.remove(listener);
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void addSearchResultListener(ISearchResultListener listener) {
        if (listener == null || this.searchResultListeners.contains(listener)) {
            return;
        }
        this.searchResultListeners.add(listener);
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeSearchResultListener(ISearchResultListener listener) {
        if (listener != null) {
            this.searchResultListeners.remove(listener);
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void addSuggestDataListener(ISuggestionResultListener listener) {
        if (listener == null || this.suggestionResultListeners.contains(listener)) {
            return;
        }
        this.suggestionResultListeners.add(listener);
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeSuggestDataListener(ISuggestionResultListener listener) {
        if (listener != null) {
            this.suggestionResultListeners.remove(listener);
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void addLocationUpdateCallBack(ILocationUpdateCallBack callBack) {
        if (callBack == null || this.locationUpdateCallBacks.contains(callBack)) {
            return;
        }
        this.locationUpdateCallBacks.add(callBack);
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeLocationUpdateCallBack(ILocationUpdateCallBack callBack) {
        if (callBack != null) {
            this.locationUpdateCallBacks.remove(callBack);
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void addPageChangedListener(final IPageChangedListener listener) {
        if (listener != null) {
            if (!this.pageChangedListeners.contains(listener)) {
                this.pageChangedListeners.add(listener);
            }
            Log.d(TAG, "==========注册页面切换监听========" + this.pageChangedListeners.size());
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$sxIM9IZ-SeegTofKHHwFY9z3dgY
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.addPageChangedListener$lambda$40(ExtendApi.this, listener);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void addPageChangedListener$lambda$40(ExtendApi this$0, IPageChangedListener iPageChangedListener) {
        Intrinsics.checkNotNullParameter(this$0, "this$0");
        ExtendServiceInterface extendServiceInterface = this$0.extendServiceInterface;
        if (extendServiceInterface != null) {
            iPageChangedListener.onPageChanged(extendServiceInterface.getPageId());
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void removePageChangedListener(IPageChangedListener listener) {
        if (listener != null) {
            this.pageChangedListeners.remove(listener);
            Log.d(TAG, "==========移除页面切换监听========" + this.pageChangedListeners.size());
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void setVoiceBtnCallBack(IVoiceBtnCallBack listener) {
        this.voiceClickListener = listener;
    }

    @Override // com.example.mxextend.IExtendApi
    public void getHomeOrCompanyData(int type, final IExtendCallback<LocationInfo> callback) {
        Intrinsics.checkNotNullParameter(callback, "callback");
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            ResponseData homeOrCompanyData = extendServiceInterface != null ? extendServiceInterface.getHomeOrCompanyData(type) : null;
            boolean z = false;
            if (homeOrCompanyData != null && homeOrCompanyData.getResponseCode() == 10000) {
                z = true;
            }
            if (z) {
                Log.d(TAG, "getHomeOrCompanyData:callback=" + homeOrCompanyData + ".data");
                Object fromJson = this.gson.fromJson(homeOrCompanyData.getData(), (Class<Object>) LocationInfo.class);
                Intrinsics.checkNotNullExpressionValue(fromJson, "gson.fromJson<LocationIn…ss.java\n                )");
                callback.success((LocationInfo) fromJson);
                return;
            }
            Integer valueOf = homeOrCompanyData != null ? Integer.valueOf(homeOrCompanyData.getResponseCode()) : null;
            Intrinsics.checkNotNull(valueOf);
            callback.onFail(new ExtendErrorModel(valueOf.intValue(), ""));
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "getHomeOrCompanyData: exception");
            e.printStackTrace();
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$LnpTUBLElZpoiLHv0XaUlwGHDiE
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.getHomeOrCompanyData$lambda$41(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void getHomeOrCompanyData$lambda$41(IExtendCallback callback) {
        Intrinsics.checkNotNullParameter(callback, "$callback");
        callback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public int getPassPointNum() {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Intrinsics.checkNotNull(extendServiceInterface);
            i = extendServiceInterface.getPassPointNum();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "getPassPointNum: resultcode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public void isExistInCollect(String name, final IExtendCallback<CollectInfoModel> callback) {
        if (callback == null) {
            Log.d(TAG, "isExistInCollect:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Intrinsics.checkNotNull(extendServiceInterface);
            final ResponseData isExistInCollect = extendServiceInterface.isExistInCollect(name + "");
            Log.d(TAG, "isExistInCollect==" + this.gson.toJson(isExistInCollect));
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$tB8HAbkV1qw8-o8tSeLljkatirE
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.isExistInCollect$lambda$42(ResponseData.this, callback);
                }
            });
        } catch (RemoteException e) {
            Log.d(TAG, "isExistInCollect: exception");
            e.printStackTrace();
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$OKuvV6js9eOkU04qBfNmRqOBV3k
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.isExistInCollect$lambda$43(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void isExistInCollect$lambda$42(ResponseData responseData, IExtendCallback iExtendCallback) {
        if (responseData.getResponseCode() == 10000) {
            int i = -1;
            CollectInfoModel collectInfoModel = new CollectInfoModel();
            try {
                Integer valueOf = Integer.valueOf(responseData.getData());
                Intrinsics.checkNotNullExpressionValue(valueOf, "valueOf(data.data)");
                i = valueOf.intValue();
            } catch (Exception unused) {
            }
            collectInfoModel.setCollcetType(i);
            iExtendCallback.success(collectInfoModel);
            return;
        }
        iExtendCallback.onFail(new ExtendErrorModel(responseData.getResponseCode(), ""));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void isExistInCollect$lambda$43(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public void setNaviScreen(final IExtendCallback<ExtendBaseModel> callback) {
        String str = TAG;
        Log.d(str, "======拉起导航到前台====");
        if (callback == null) {
            Log.d(str, "setNaviScreen:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.setNaviScreen(new IServiceCallBack.Stub() { // from class: com.example.mxextend.ExtendApi$setNaviScreen$1
                    @Override // com.mxnavi.busines.IServiceCallBack
                    public void onResponse(int responseCode, String response) throws RemoteException {
                        String str2;
                        Intrinsics.checkNotNullParameter(response, "response");
                        str2 = ExtendApi.TAG;
                        Log.d(str2, "setNaviScreen:" + responseCode);
                        if (responseCode == 10000) {
                            callback.success(new ExtendBaseModel());
                        } else {
                            callback.onFail(new ExtendErrorModel(responseCode, ""));
                        }
                    }
                });
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "setNaviScreen: exception");
            e.printStackTrace();
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$daBXJiV0edpoD8nQo-scEWiHQms
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.setNaviScreen$lambda$44(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void setNaviScreen$lambda$44(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public void setDayNightStyle(int style, IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "setDayNightStyle:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.setDayNightStyle(style)) : null;
            Intrinsics.checkNotNull(valueOf);
            int intValue = valueOf.intValue();
            Log.d(TAG, "setDayNightStyle: resultcode=" + intValue);
            if (intValue == 10000) {
                callback.success(new ExtendBaseModel());
            } else {
                callback.onFail(new ExtendErrorModel(intValue, ""));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            callback.onFail(new ExtendErrorModel(-2, ""));
            Log.d(TAG, "setDayNightStyle:exception");
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void getSearchHistoryData(final IExtendCallback<SearchResultModel> callback) {
        if (callback == null) {
            Log.d(TAG, "getSearchHistoryData:callback= null");
            return;
        }
        try {
            if (!this.isBind) {
                callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
                return;
            }
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.getSearchHistory(new ExtendApi$getSearchHistoryData$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "getSearchHistoryData: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$mTl5V0SID9lDpc3Mqy7qOQAyEGE
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.getSearchHistoryData$lambda$45(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void getSearchHistoryData$lambda$45(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public void getFavoriteList(final IExtendCallback<SearchResultModel> callback) {
        if (callback == null) {
            Log.d(TAG, "getFavoriteList:callback= null");
            return;
        }
        try {
            if (!this.isBind) {
                callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
                return;
            }
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.getFavoriteList(new ExtendApi$getFavoriteList$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "getFavoriteList: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$GdyGxWGvuY8A9qkBGxbo17D9w64
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.getFavoriteList$lambda$46(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void getFavoriteList$lambda$46(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public void getRouteSummaryList(final IExtendCallback<RouteSummaryModel> callback) {
        if (callback == null) {
            Log.d(TAG, "getRouteSummaryList:callback= null");
            return;
        }
        try {
            if (!this.isBind) {
                callback.onFail(new ExtendErrorModel(-1, "服务未启动"));
                return;
            }
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.getRouteSummaryList(new ExtendApi$getRouteSummaryList$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "getRouteSummaryList: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$qOwok-Gdf864kUFSf0JN4tHzAHY
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.getRouteSummaryList$lambda$47(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void getRouteSummaryList$lambda$47(IExtendCallback iExtendCallback) {
        iExtendCallback.onFail(new ExtendErrorModel(-2, "请求发生异常"));
    }

    @Override // com.example.mxextend.IExtendApi
    public void goHomeOrCompany(int destType, int directNavi, final IExtendCallback<ExtendBaseModel> callback) {
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Intrinsics.checkNotNull(extendServiceInterface);
            final int goHomeOrCompany = extendServiceInterface.goHomeOrCompany(destType, directNavi);
            Log.d(TAG, "specialPoiNavi: resultcode=" + goHomeOrCompany);
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$Tx8gSkeSCCoE873V8Jw5FqrjUhU
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.goHomeOrCompany$lambda$48(goHomeOrCompany, callback);
                }
            });
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "specialPoiNavi: exception");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$RUEuj1UTosJ5cZyQf3XH93NnHqM
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.goHomeOrCompany$lambda$49(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void goHomeOrCompany$lambda$48(int i, IExtendCallback iExtendCallback) {
        if (i == 10000) {
            Intrinsics.checkNotNull(iExtendCallback);
            iExtendCallback.success(new ExtendBaseModel());
        } else {
            Intrinsics.checkNotNull(iExtendCallback);
            iExtendCallback.onFail(new ExtendErrorModel(i, ""));
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void goHomeOrCompany$lambda$49(IExtendCallback iExtendCallback) {
        Intrinsics.checkNotNull(iExtendCallback);
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int goFavorite() {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.goFavorite()) : null;
            Intrinsics.checkNotNull(valueOf);
            i = valueOf.intValue();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "goTeamTrip: resultcode=" + i);
        return 0;
    }

    @Override // com.example.mxextend.IExtendApi
    public void addHomeOrCompanyChangedListener(IHomeOrCompanyListener listener) {
        if (listener == null || this.homeOrCompanyListeners.contains(listener)) {
            return;
        }
        this.homeOrCompanyListeners.add(listener);
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeHomeOrCompanyChangedListener(IHomeOrCompanyListener listener) {
        if (listener != null) {
            this.homeOrCompanyListeners.remove(listener);
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public int getNaviStage() {
        int i;
        if (!this.isBind) {
            Log.d(TAG, "getNaviStage: unbindserivce");
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.getNaviStage()) : null;
            Intrinsics.checkNotNull(valueOf);
            i = valueOf.intValue();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "getNaviStage: resultcode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public void addWeatherInfoListenerListener(IWeatherInfoListener listener) {
        if (listener == null || this.weatherInfoListeners.contains(listener)) {
            return;
        }
        this.weatherInfoListeners.add(listener);
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeWeatherInfoListenerListener(IWeatherInfoListener listener) {
        if (listener != null) {
            this.weatherInfoListeners.remove(listener);
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public int isInNavi() {
        int i;
        if (!this.isBind) {
            return -1;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Integer valueOf = extendServiceInterface != null ? Integer.valueOf(extendServiceInterface.isInNavi()) : null;
            Intrinsics.checkNotNull(valueOf);
            i = valueOf.intValue();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "bringAppToFront: resultcode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public void getEndPoint(final IExtendCallback<LocationInfo> callback) {
        Intrinsics.checkNotNullParameter(callback, "callback");
        String str = TAG;
        Log.d(str, "======================获取终点========================1.0.31");
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "获取终点 服务未启动"));
            return;
        }
        try {
            Log.d(str, "======================获取终点========================1");
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.getEndPoint(new ExtendApi$getEndPoint$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "======================获取终点========================2");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$ZhTIF0MSncd3l_sekFFJEKYD34s
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.getEndPoint$lambda$50(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void getEndPoint$lambda$50(IExtendCallback callback) {
        Intrinsics.checkNotNullParameter(callback, "$callback");
        Log.d(TAG, "======================获取终点 1========================3");
        callback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public void lightFigureSwitch(int type, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "setDayNightStyle:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.lightFigureSwitch(type, new IServiceCallBack.Stub() { // from class: com.example.mxextend.ExtendApi$lightFigureSwitch$1
                    @Override // com.mxnavi.busines.IServiceCallBack
                    public void onResponse(int responseCode, String response) {
                        String str;
                        str = ExtendApi.TAG;
                        Log.d(str, "lightFigureSwitch: resultcode=" + responseCode);
                        if (responseCode == 10000) {
                            callback.success(new ExtendBaseModel());
                        } else {
                            callback.onFail(new ExtendErrorModel(responseCode, ""));
                        }
                    }
                });
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            callback.onFail(new ExtendErrorModel(-2, ""));
            Log.d(TAG, "setDayNightStyle:exception");
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void deleteViaPoint(int index, final IExtendCallback<LocationInfo> callback) {
        String str = TAG;
        Log.d(str, "======================deleteViaPoint删除途经点========================1.0.31");
        if (callback == null) {
            Log.d(str, "deleteViaPoint删除途经点:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, "删除途经点 服务未启动"));
            return;
        }
        try {
            Log.d(str, "======================删除途经点========================1");
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.deleteViaPoint(index, new ExtendApi$deleteViaPoint$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            Log.d(TAG, "======================删除途经点========================2");
            this.mainHandler.post(new Runnable() { // from class: com.example.mxextend.-$$Lambda$ExtendApi$BMCtDqCvs--s2bjUZxtkU84JjW4
                @Override // java.lang.Runnable
                public final void run() {
                    ExtendApi.deleteViaPoint$lambda$51(IExtendCallback.this);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static final void deleteViaPoint$lambda$51(IExtendCallback iExtendCallback) {
        Log.d(TAG, "======================删除途经点 ========================3");
        iExtendCallback.onFail(new ExtendErrorModel(-2, ""));
    }

    @Override // com.example.mxextend.IExtendApi
    public int cancelNaviBack() {
        ExtendServiceInterface extendServiceInterface;
        int i;
        if (!this.isBind || (extendServiceInterface = this.extendServiceInterface) == null) {
            return -1;
        }
        try {
            Intrinsics.checkNotNull(extendServiceInterface);
            i = extendServiceInterface.cancelNaviBack();
        } catch (RemoteException e) {
            e.printStackTrace();
            i = -2;
        }
        Log.d(TAG, "cancelNaviBack: resultcode=" + i);
        return i;
    }

    @Override // com.example.mxextend.IExtendApi
    public void changeInstrumentShowMode(int showMode) {
        this.mShowMode = showMode;
        if (this.extendServiceInterface == null) {
            Log.d(TAG, "changeInstrumentShowMode: extendServiceInterface is Null");
            return;
        }
        if (this.displaySurfaceView == null) {
            initDisplaySurfaceView(showMode);
        }
        String str = TAG;
        StringBuilder append = new StringBuilder().append("changeInstrumentShowMode:lastShowMode:");
        DisplaySurfaceView displaySurfaceView = this.displaySurfaceView;
        Intrinsics.checkNotNull(displaySurfaceView);
        Log.d(str, append.append(displaySurfaceView.getShowMode()).append(" currentShowMode:").append(showMode).toString());
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            Intrinsics.checkNotNull(extendServiceInterface);
            extendServiceInterface.setInstrumentShowMode(showMode);
            DisplaySurfaceView displaySurfaceView2 = this.displaySurfaceView;
            Intrinsics.checkNotNull(displaySurfaceView2);
            if (displaySurfaceView2.getShowMode() != showMode) {
                DisplaySurfaceView displaySurfaceView3 = this.displaySurfaceView;
                Intrinsics.checkNotNull(displaySurfaceView3);
                displaySurfaceView3.changeShowMode(showMode);
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void restoreNavigation(int type, final IExtendCallback<HistoricaRouteInfo> callback) {
        if (callback == null) {
            Log.d(TAG, "restoreNavigation:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.restoreNavigation(type, new IServiceCallBack.Stub() { // from class: com.example.mxextend.ExtendApi$restoreNavigation$1
                    @Override // com.mxnavi.busines.IServiceCallBack
                    public void onResponse(int responseCode, String response) {
                        String str;
                        str = ExtendApi.TAG;
                        Log.d(str, "restoreNavigation: resultcode=" + responseCode + " ===response：" + response);
                        HistoricaRouteInfo historicaRouteInfo = new HistoricaRouteInfo();
                        historicaRouteInfo.setExtendId(responseCode);
                        historicaRouteInfo.setHistoricaRouteName(response);
                        if (responseCode == 10000) {
                            callback.success(historicaRouteInfo);
                        } else if (responseCode == 10020 || responseCode == 10806) {
                            callback.success(historicaRouteInfo);
                        } else {
                            callback.onFail(new ExtendErrorModel(responseCode, ""));
                        }
                    }
                });
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            callback.onFail(new ExtendErrorModel(-2, ""));
            Log.d(TAG, "restoreNavigation:exception");
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void switchParallelRoad(int operaType, final IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "switchParallelRoad:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.switchParallelRoad(operaType, new IServiceCallBack.Stub() { // from class: com.example.mxextend.ExtendApi$switchParallelRoad$1
                    @Override // com.mxnavi.busines.IServiceCallBack
                    public void onResponse(int responseCode, String response) {
                        String str;
                        str = ExtendApi.TAG;
                        Log.d(str, "switchParallelRoad: resultcode=" + responseCode);
                        if (responseCode == 10000) {
                            callback.success(new ExtendBaseModel());
                        } else {
                            callback.onFail(new ExtendErrorModel(responseCode, ""));
                        }
                    }
                });
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            callback.onFail(new ExtendErrorModel(-2, ""));
            Log.d(TAG, "switchParallelRoad:exception");
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void switchRoute(IExtendCallback<ExtendBaseModel> callback) {
        if (callback == null) {
            Log.d(TAG, "switchParallelRoad:callback= null");
            return;
        }
        if (!this.isBind) {
            callback.onFail(new ExtendErrorModel(-1, ""));
            return;
        }
        try {
            ExtendServiceInterface extendServiceInterface = this.extendServiceInterface;
            if (extendServiceInterface != null) {
                extendServiceInterface.switchRoute(new ExtendApi$switchRoute$1(this, callback));
            }
        } catch (RemoteException e) {
            e.printStackTrace();
            callback.onFail(new ExtendErrorModel(-2, ""));
            Log.d(TAG, "switchParallelRoad:exception");
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void addManualVoiceInteractionListener(IManualVoiceInteractionListener listener) {
        if (listener != null) {
            if (!this.manualVoiceInteractionListeners.contains(listener)) {
                this.manualVoiceInteractionListeners.add(listener);
            }
            if (this.manualVoiceInteractionListeners != null) {
                Log.d(TAG, "==========注册手动语音交互监听========" + this.manualVoiceInteractionListeners.size());
            }
        }
    }

    @Override // com.example.mxextend.IExtendApi
    public void removeManualVoiceInteractionListener(IManualVoiceInteractionListener listener) {
        if (listener != null) {
            this.manualVoiceInteractionListeners.remove(listener);
            if (this.manualVoiceInteractionListeners != null) {
                Log.d(TAG, "==========移除手动语音交互监听========" + this.manualVoiceInteractionListeners.size());
            }
        }
    }
}
