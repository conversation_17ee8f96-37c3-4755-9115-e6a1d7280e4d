package com.chinatsp.dashboard.model;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.database.ContentObserver;
import android.net.Uri;
import android.provider.Settings;
import android.text.TextUtils;
import com.chinatsp.dashboard.utils.HandlerUtil;
import com.chinatsp.dashboard.utils.Logcat;
import com.chinatsp.dashboard.utils.MediaConstants;
import com.chinatsp.dashboard.utils.ThemeChangeHelp;

/* loaded from: classes.dex */
public class InteractiveModel implements IInteractiveProtocol {
    public static final String CURRENT_SPLIT_SCREEN_MODE = "key_multi_window_state";
    private static final String NAVI_PAGE_CHANGE_KEY = "com.chinatsp.systemui.NAVI_PAGE";
    public static final String PACKAGE_FITNESS = "com.Oshan.Fitness";
    public static final String PACKAGE_SETTINGS = "com.chinatsp.settings";
    private static final int PAGE_CHANGE_DELAY = 400;
    private static final String PKG_AIR_CONDITIONER = "com.os.airconditioner";
    private static final String PKG_CURR_TOP_KEY = "com.chinatsp.systemui.cur_package";
    private static final String PKG_NAVI = "ru.yandex.yandexnavi";
    private static final String PKG_SETTING = "com.chinatsp.settings";
    public static final String SCREEN_CLOCK_STATUE = "screen_clock";
    public static final int SCREEN_STATUE_IN_CLOCK = 1;
    public static final int SCREEN_STATUE_NORMAL = 0;
    public static final String SHOW_MODE = "show_mode";
    public static final int SPLIT_MODE_FULL_SCREEN = 0;
    public static final int SPLIT_MODE_SPLIT_SCREEN = 1;
    private static final String SYSTEM_NOTIFY_CLOSE_SPLIT = "system_notify_close_split";
    private static final String TAG = "InteractiveModel";
    private ICarServiceProtocol carServiceProtocol;
    private final Context context;
    private INaviProtocol naviProtocol;
    private String lastTopPackage = "";
    private String currentTopPackage = "";
    private volatile boolean naviMultiScreenState = true;
    private final Runnable topAppChangeTask = new Runnable() { // from class: com.chinatsp.dashboard.model.-$$Lambda$InteractiveModel$7mHRIbKgC0axCq1eL5uNSuFerWI
        @Override // java.lang.Runnable
        public final void run() {
            InteractiveModel.this.lambda$new$0$InteractiveModel();
        }
    };
    private final Runnable clearCarServiceTask = new Runnable() { // from class: com.chinatsp.dashboard.model.-$$Lambda$InteractiveModel$yW4IvBt2h1MpDrtCd5Mt8_tS7e8
        @Override // java.lang.Runnable
        public final void run() {
            InteractiveModel.this.lambda$new$1$InteractiveModel();
        }
    };
    private final ContentObserver pageChangeObserver = new ContentObserver(HandlerUtil.getThreadHandler()) { // from class: com.chinatsp.dashboard.model.InteractiveModel.1
        @Override // android.database.ContentObserver
        public void onChange(boolean z, Uri uri) {
            super.onChange(z, uri);
            if (uri == null) {
                return;
            }
            Logcat.d(InteractiveModel.TAG, "pageChangeObserver onChange uri:" + uri);
            String uri2 = uri.toString();
            if (TextUtils.isEmpty(uri2)) {
                return;
            }
            if (uri2.contains(InteractiveModel.PKG_CURR_TOP_KEY)) {
                Logcat.d(InteractiveModel.TAG, "pageObserver  =com.chinatsp.systemui.cur_package");
                InteractiveModel.this.runTopAppChangTask();
                return;
            }
            if (uri2.contains(InteractiveModel.CURRENT_SPLIT_SCREEN_MODE)) {
                if (!InteractiveModel.this.isSplitScreenMode()) {
                    Logcat.d(InteractiveModel.TAG, "splitObserver  =0");
                    InteractiveModel.this.runTopAppChangTask();
                    return;
                } else {
                    InteractiveModel.this.runTopAppChangTask();
                    return;
                }
            }
            if (uri2.contains(InteractiveModel.SCREEN_CLOCK_STATUE)) {
                Logcat.d(InteractiveModel.TAG, "screenClockObserver  =screen_clock");
                InteractiveModel.this.runTopAppChangTask();
            } else if (uri2.contains(InteractiveModel.SHOW_MODE)) {
                Logcat.d(InteractiveModel.TAG, "themeObserver  =show_mode");
                InteractiveModel.this.carServiceProtocol.themeChange();
            }
        }
    };
    private boolean jumpToNavi2ByAvm = false;
    private boolean lastAvmDisplayShow = false;

    public interface ICarServiceProtocol extends IProtocol {
        void exitFullNaviPresentation();

        boolean isAvmOn();

        boolean isLightShowing();

        boolean isMeterNaviRunning();

        boolean isRestMode();

        boolean isTrackRunning();

        void naviStateChanged(boolean z, boolean z2);

        void removeFullNaviChangeTask();

        void requestMeterFullNavi(boolean z);

        void requestMeterSimpleNavi(boolean z);

        void themeChange();
    }

    public interface INaviProtocol extends IProtocol {
        boolean isNaviRunning();
    }

    public interface IProtocol {
        void setInteractiveProtocol(IInteractiveProtocol iInteractiveProtocol);
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void exitSimpleNavi() {
    }

    public /* synthetic */ void lambda$new$0$InteractiveModel() {
        String topAppPkg = getTopAppPkg();
        this.lastTopPackage = this.currentTopPackage;
        this.currentTopPackage = topAppPkg;
        Logcat.d(TAG, "pageChangeObserver onChange topApp:" + topAppPkg);
        topAppChange(topAppPkg);
    }

    public /* synthetic */ void lambda$new$1$InteractiveModel() {
        ICarServiceProtocol iCarServiceProtocol = this.carServiceProtocol;
        if (iCarServiceProtocol != null) {
            iCarServiceProtocol.removeFullNaviChangeTask();
        }
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void runTopAppChangTask() {
        HandlerUtil.getMainHandler().removeCallbacks(this.topAppChangeTask);
        if (isNaviTop()) {
            HandlerUtil.getMainHandler().postDelayed(this.topAppChangeTask, 250L);
        }
        HandlerUtil.getMainHandler().postDelayed(this.topAppChangeTask, 400L);
    }

    public InteractiveModel(Context context) {
        this.context = context.getApplicationContext();
    }

    private String getTopAppPkg() {
        String string = Settings.System.getString(this.context.getContentResolver(), PKG_CURR_TOP_KEY);
        Logcat.d("getTopAppPkg pkg:" + string);
        return string;
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public boolean isNaviTop() {
        boolean equals = TextUtils.equals("ru.yandex.yandexnavi", getTopAppPkg());
        Logcat.d("isNaviTop result: " + equals);
        return equals;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public boolean isSplitScreenMode() {
        int i = Settings.System.getInt(this.context.getContentResolver(), CURRENT_SPLIT_SCREEN_MODE, 0);
        Logcat.d("getSplitScreenMode screenMode:" + i);
        return i == 1;
    }

    private boolean isScreenClockMode() {
        int i = Settings.System.getInt(this.context.getContentResolver(), SCREEN_CLOCK_STATUE, 0);
        Logcat.d("getSplitScreenMode screenClock:" + i);
        return i == 1;
    }

    private void topAppChange(String str) {
        // Обновляем состояние навигации для Яндекс.Навигатора
        updateYandexNaviState(str);

        if (this.carServiceProtocol == null) {
            Logcat.d(TAG, "topAppChange fail. carServiceProtocol is null. pkg:" + str);
            return;
        }
        if (this.naviProtocol.isNaviRunning()) {
            if (isScreenClockMode()) {
                Logcat.d("in screenClock mode");
                if (this.carServiceProtocol.isMeterNaviRunning() || this.carServiceProtocol.isRestMode()) {
                    return;
                }
                Logcat.d("in screenClock mode requestMeterNavi");
                requestMeterNavi(true);
                return;
            }
            Logcat.d("topAppChange pkg=" + str);
            if (isSplitScreenMode() || TextUtils.equals(MediaConstants.PACKAGE_SYSTEM_UI, str)) {
                if (this.carServiceProtocol.isMeterNaviRunning()) {
                    Logcat.d("request meter navi exit");
                    requestMeterNavi(false);
                    if (this.carServiceProtocol.isRestMode()) {
                        return;
                    }
                    this.carServiceProtocol.exitFullNaviPresentation();
                    return;
                }
                return;
            }
            Logcat.d("out Split Screen Mode");
            if (!TextUtils.equals("ru.yandex.yandexnavi", str)) {
                if (this.carServiceProtocol.isMeterNaviRunning()) {
                    return;
                }
                Logcat.d("request meter navi");
                if (this.carServiceProtocol.isRestMode()) {
                    return;
                }
                requestMeterNavi(true);
                return;
            }
            if (this.carServiceProtocol.isMeterNaviRunning()) {
                Logcat.d("request meter navi exit");
                if (this.carServiceProtocol.isAvmOn()) {
                    return;
                }
                requestMeterNavi(false);
                if (this.carServiceProtocol.isRestMode()) {
                    return;
                }
                this.carServiceProtocol.exitFullNaviPresentation();
            }
        }
    }

    private void exitOtherNavi() {
        Logcat.d(TAG, "exitOtherNavi");
        ICarServiceProtocol iCarServiceProtocol = this.carServiceProtocol;
        if (iCarServiceProtocol == null || !iCarServiceProtocol.isMeterNaviRunning()) {
            return;
        }
        this.carServiceProtocol.requestMeterFullNavi(false);
    }

    public void register() {
        this.context.getContentResolver().registerContentObserver(Settings.System.getUriFor(PKG_CURR_TOP_KEY), false, this.pageChangeObserver);
        this.context.getContentResolver().registerContentObserver(Settings.System.getUriFor(CURRENT_SPLIT_SCREEN_MODE), false, this.pageChangeObserver);
        this.context.getContentResolver().registerContentObserver(Settings.System.getUriFor(SCREEN_CLOCK_STATUE), false, this.pageChangeObserver);
        this.context.getContentResolver().registerContentObserver(Settings.System.getUriFor(SHOW_MODE), false, this.pageChangeObserver);
    }

    public void unregister() {
        this.context.getContentResolver().unregisterContentObserver(this.pageChangeObserver);
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void setNaviProtocol(INaviProtocol iNaviProtocol) {
        Logcat.d(TAG, "setNaviProtocol naviProtocol:" + iNaviProtocol);
        this.naviProtocol = iNaviProtocol;
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void setCarServiceProtocol(ICarServiceProtocol iCarServiceProtocol) {
        Logcat.d(TAG, "setCarServiceProtocol carServiceProtocol:" + iCarServiceProtocol);
        this.carServiceProtocol = iCarServiceProtocol;
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public boolean meterFullNaviRequest(boolean z) {
        INaviProtocol iNaviProtocol = this.naviProtocol;
        if (iNaviProtocol == null) {
            Logcat.d(TAG, "meterFullNaviRequest fail. naviProtocol is null. isEnterOrExit:" + z);
            return false;
        }
        if (this.carServiceProtocol == null) {
            Logcat.d(TAG, "meterFullNaviRequest fail. carServiceProtocol is null. isEnterOrExit:" + z);
            return false;
        }
        boolean isNaviRunning = iNaviProtocol.isNaviRunning();
        if (!isNaviRunning) {
            Logcat.d(TAG, "meterFullNaviRequest fail. navi is not running.");
            exitOtherNavi();
            return false;
        }
        if (this.carServiceProtocol.isTrackRunning() || this.carServiceProtocol.isRestMode()) {
            return false;
        }
        this.carServiceProtocol.requestMeterFullNavi(z);
        Logcat.d(TAG, "meterFullNaviRequest isNaviRunning:" + isNaviRunning + " , isEnterOrExit:" + z);
        return true;
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public boolean isJumpToNavi2ByAvm() {
        return this.jumpToNavi2ByAvm;
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void setJumpToNavi2ByAvm(boolean z) {
        this.jumpToNavi2ByAvm = z;
        Logcat.d(TAG, "avmDisplaySwitch reset jumpToNavi2ByAvm = " + z);
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void avmDisplaySwitch(boolean z) {
        try {
            INaviProtocol iNaviProtocol = this.naviProtocol;
            if (iNaviProtocol == null) {
                Logcat.d(TAG, "avmDisplaySwitch fail. naviProtocol is null. isShow:" + z);
                return;
            }
            if (this.carServiceProtocol == null) {
                return;
            }
            if (!iNaviProtocol.isNaviRunning()) {
                Logcat.d(TAG, "avmDisplaySwitch not response. navi is not running.");
                exitOtherNavi();
                return;
            }
            boolean isNaviTop = isNaviTop();
            if (this.carServiceProtocol.isMeterNaviRunning()) {
                Logcat.d(TAG, "avmDisplaySwitch meter navi is running. isShow:" + z + " , jumpToNavi2ByAvm:" + this.jumpToNavi2ByAvm);
                if (z) {
                    if (isNaviTop) {
                        startFunctionDesk();
                        this.jumpToNavi2ByAvm = true;
                    } else {
                        Logcat.d(TAG, "avmDisplaySwitch  meter navi is running. navi is’t top");
                        Logcat.d(TAG, "avmDisplaySwitch not response. meter navi is running.");
                    }
                } else if (this.jumpToNavi2ByAvm) {
                    if (isNaviTop) {
                        this.carServiceProtocol.requestMeterFullNavi(false);
                    } else {
                        startNaviDesk();
                    }
                    this.jumpToNavi2ByAvm = false;
                    Logcat.d(TAG, "avmDisplaySwitch reset jumpToNavi2ByAvm");
                } else {
                    if (isNaviTop) {
                        this.carServiceProtocol.requestMeterFullNavi(false);
                    }
                    Logcat.d(TAG, " jumpToNavi2ByAvm = false , don‘t do anything.");
                }
            } else {
                Logcat.d(TAG, "avmDisplaySwitch response. isShow:" + z + " , jumpToNavi2ByAvm:" + this.jumpToNavi2ByAvm);
                if (!z) {
                    if (this.jumpToNavi2ByAvm) {
                        startNaviDesk();
                        Logcat.d(TAG, "avmDisplaySwitch reset startNaviDesk");
                    }
                    Logcat.d(TAG, "avmDisplaySwitch reset jumpToNavi2ByAvm");
                    this.jumpToNavi2ByAvm = false;
                } else if (isNaviTop) {
                    startFunctionDesk();
                    this.jumpToNavi2ByAvm = true;
                } else {
                    this.jumpToNavi2ByAvm = false;
                    Logcat.d(TAG, "avmDisplaySwitch reset jumpToNavi2ByAvm");
                }
            }
        } finally {
            this.lastAvmDisplayShow = z;
        }
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void naviStateChange(boolean z) {
        Logcat.d(TAG, "naviStateChange isNavi:" + z);
        if (this.naviProtocol == null) {
            Logcat.d(TAG, "naviStateChange fail. naviProtocol is null. isNavi:" + z);
            return;
        }
        ICarServiceProtocol iCarServiceProtocol = this.carServiceProtocol;
        if (iCarServiceProtocol == null) {
            Logcat.d(TAG, "naviStateChange fail. carServiceProtocol is null. isNavi:" + z);
        } else {
            iCarServiceProtocol.naviStateChanged(z, ThemeChangeHelp.isNight(this.context));
        }
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void requestMeterSimpleNavi(boolean z) {
        Logcat.d(TAG, "requestMeterSimpleNavi isShowOrHide:" + z);
        INaviProtocol iNaviProtocol = this.naviProtocol;
        if (iNaviProtocol == null) {
            Logcat.d(TAG, "requestMeterSimpleNavi fail. naviProtocol is null.");
            return;
        }
        if (this.carServiceProtocol == null) {
            Logcat.d(TAG, "requestMeterSimpleNavi fail. carServiceProtocol is null.");
        } else if (!iNaviProtocol.isNaviRunning()) {
            Logcat.d(TAG, "requestMeterSimpleNavi not response. navi is not running.");
            exitOtherNavi();
        } else {
            this.carServiceProtocol.requestMeterSimpleNavi(z);
        }
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void requestMeterNavi(boolean z) {
        Logcat.d(TAG, "requestMeterNavi isShowOrHide:" + z);
        INaviProtocol iNaviProtocol = this.naviProtocol;
        if (iNaviProtocol == null) {
            Logcat.d(TAG, "requestMeterNavi fail. naviProtocol is null.");
            return;
        }
        if (this.carServiceProtocol == null) {
            Logcat.d(TAG, "requestMeterNavi fail. carServiceProtocol is null.");
        } else if (!iNaviProtocol.isNaviRunning()) {
            Logcat.d(TAG, "requestMeterNavi not response. navi is not running.");
            exitOtherNavi();
        } else {
            this.carServiceProtocol.requestMeterFullNavi(z);
        }
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void receiveMeterStateChange(boolean z) {
        INaviProtocol iNaviProtocol = this.naviProtocol;
        if (iNaviProtocol == null) {
            Logcat.d(TAG, "receiveMeterNaviStateChange fail. naviProtocol is null. isShowOrHide:" + z);
            return;
        }
        if (this.carServiceProtocol == null) {
            Logcat.d(TAG, "receiveMeterNaviStateChange fail. carServiceProtocol is null. isShowOrHide:" + z);
            return;
        }
        if (!iNaviProtocol.isNaviRunning()) {
            Logcat.d(TAG, "receiveMeterNaviStateChange not response. navi is not running.");
            exitOtherNavi();
        } else if (isScreenClockMode()) {
            Logcat.d(TAG, "receive meter navi state change is in screen clock mode.");
        } else if (TextUtils.equals("ru.yandex.yandexnavi", getTopAppPkg())) {
            Logcat.d("startFunctionDesk");
            this.carServiceProtocol.requestMeterFullNavi(false);
            this.carServiceProtocol.exitFullNaviPresentation();
        }
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void meterNaviStateChange(boolean z) {
        INaviProtocol iNaviProtocol = this.naviProtocol;
        if (iNaviProtocol == null) {
            Logcat.d(TAG, "meterNaviStateChange fail. naviProtocol is null. isShowOrHide:" + z);
            return;
        }
        if (this.carServiceProtocol == null) {
            Logcat.d(TAG, "meterNaviStateChange fail. carServiceProtocol is null. isShowOrHide:" + z);
            return;
        }
        if (!iNaviProtocol.isNaviRunning()) {
            Logcat.d(TAG, "meterNaviStateChange not response. navi is not running.");
            exitOtherNavi();
            return;
        }
        Logcat.d("meterNaviStateChange isShowOrHide=" + z);
        String topAppPkg = getTopAppPkg();
        if (z) {
            if (isSplitScreenMode()) {
                int i = Settings.Global.getInt(this.context.getContentResolver(), SYSTEM_NOTIFY_CLOSE_SPLIT, 2);
                Logcat.d(" in Split Screen isOutRightSplit=" + i);
                if (i == 2) {
                    Settings.Global.putInt(this.context.getContentResolver(), SYSTEM_NOTIFY_CLOSE_SPLIT, 0);
                }
                Settings.Global.putInt(this.context.getContentResolver(), SYSTEM_NOTIFY_CLOSE_SPLIT, 2);
                return;
            }
            if (TextUtils.equals("ru.yandex.yandexnavi", topAppPkg)) {
                Logcat.d("startFunctionDesk");
                startFunctionDesk();
                return;
            }
            return;
        }
        Logcat.d("startNaviDesk1");
        if (TextUtils.equals("ru.yandex.yandexnavi", topAppPkg)) {
            return;
        }
        Logcat.d("startNaviDesk from navi");
        startNaviDesk();
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void startNaviDesk() {
        Logcat.d("startNaviDesk");
        Intent launchIntentForPackage = this.context.getPackageManager().getLaunchIntentForPackage("ru.yandex.yandexnavi");
        launchIntentForPackage.addCategory("android.intent.category.DEFAULT");
        this.context.startActivity(launchIntentForPackage);
    }

    private void startFunctionDesk() {
        Logcat.d();
        Intent intent = new Intent();
        intent.addFlags(268435456);
        intent.setComponent(new ComponentName("com.chinatsp.launcher", "com.chinatsp.launcher.MainActivity"));
        this.context.startActivity(intent);
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public void upDataNaviShow() {
        if (!this.naviProtocol.isNaviRunning()) {
            Logcat.d(TAG, "upDataNaviShow not response. navi is not running.");
        } else {
            HandlerUtil.getMainHandler().post(this.clearCarServiceTask);
        }
    }

    @Override // com.chinatsp.dashboard.model.IInteractiveProtocol
    public boolean isNaviRunning() {
        INaviProtocol iNaviProtocol = this.naviProtocol;
        if (iNaviProtocol == null) {
            Logcat.d(TAG, "naviProtocol is null.");
            return false;
        }
        return iNaviProtocol.isNaviRunning();
    }

    private void updateYandexNaviState(String currentPackage) {
        if (this.naviProtocol instanceof YandexNaviModel) {
            YandexNaviModel yandexModel = (YandexNaviModel) this.naviProtocol;
            boolean isYandexNaviActive = "ru.yandex.yandexnavi".equals(currentPackage);
            yandexModel.setNaviRunning(isYandexNaviActive);
            Logcat.d(TAG, "updateYandexNaviState: " + currentPackage + " -> " + isYandexNaviActive);
        }
    }
}
