package com.chinatsp.dashboard.model;

import android.content.Context;
import android.content.pm.PackageManager;
import android.provider.Settings;
import com.chinatsp.dashboard.callback.NaviCallback;
import com.chinatsp.dashboard.model.InteractiveModel;
import com.chinatsp.dashboard.utils.HandlerUtil;
import com.chinatsp.dashboard.utils.Logcat;
import java.util.concurrent.atomic.AtomicBoolean;

/* loaded from: classes.dex */
public class YandexNaviModel implements InteractiveModel.INaviProtocol {
    private static final String TAG = "YandexNaviModel";
    private static final String YANDEX_NAVI_PACKAGE = "ru.yandex.yandexnavi";
    
    private final Context mContext;
    private NaviCallback mNaviCallback;
    private volatile IInteractiveProtocol interactiveProtocol;
    private final AtomicBoolean mNaviStarted = new AtomicBoolean(false);
    private boolean isYandexNaviInstalled = false;

    public YandexNaviModel(Context context, NaviCallback naviCallback) {
        this.mContext = context.getApplicationContext();
        setNaviCallback(naviCallback);
    }

    public void setNaviCallback(NaviCallback naviCallback) {
        this.mNaviCallback = naviCallback;
    }

    public void init() {
        Logcat.d(TAG, "YandexNaviModel init");
        checkYandexNaviInstalled();
        // Устанавливаем начальное состояние навигации
        Settings.System.putInt(this.mContext.getContentResolver(), "navi_status_to_vehicle_control", 1);
    }

    public void release() {
        Logcat.d(TAG, "YandexNaviModel release");
        this.mNaviCallback = null;
        this.interactiveProtocol = null;
    }

    private void checkYandexNaviInstalled() {
        try {
            PackageManager pm = this.mContext.getPackageManager();
            pm.getPackageInfo(YANDEX_NAVI_PACKAGE, PackageManager.GET_ACTIVITIES);
            this.isYandexNaviInstalled = true;
            Logcat.d(TAG, "Yandex Navigator is installed");
        } catch (PackageManager.NameNotFoundException e) {
            this.isYandexNaviInstalled = false;
            Logcat.w(TAG, "Yandex Navigator is not installed");
        }
    }

    @Override
    public boolean isNaviRunning() {
        // Простая проверка - считаем, что навигация запущена, если приложение установлено
        // и было активировано через систему
        boolean isRunning = this.isYandexNaviInstalled && this.mNaviStarted.get();
        Logcat.d(TAG, "isNaviRunning result: " + isRunning);
        return isRunning;
    }

    public void setNaviRunning(boolean running) {
        boolean wasRunning = this.mNaviStarted.get();
        this.mNaviStarted.set(running);
        
        if (wasRunning != running) {
            Logcat.d(TAG, "Navi state changed: " + running);
            naviStateChange(running);
        }
    }

    private void naviStateChange(boolean isRunning) {
        Logcat.d(TAG, "naviStateChange isRunning:" + isRunning + 
                " , mNaviCallback:" + (this.mNaviCallback != null) + 
                " , interactiveProtocol:" + (this.interactiveProtocol != null));
        
        // Обновляем системные настройки
        Settings.System.putInt(this.mContext.getContentResolver(), 
                "navi_status_to_vehicle_control", isRunning ? 0 : 1);
        
        // Уведомляем callback
        NaviCallback naviCallback = this.mNaviCallback;
        if (naviCallback != null) {
            naviCallback.naviStateChange(isRunning);
        }
        
        // Уведомляем interactive protocol
        if (this.interactiveProtocol != null) {
            this.interactiveProtocol.naviStateChange(isRunning);
        }
    }

    @Override
    public void setInteractiveProtocol(IInteractiveProtocol iInteractiveProtocol) {
        Logcat.d(TAG, "setInteractiveProtocol protocol:" + iInteractiveProtocol);
        synchronized (this) {
            this.interactiveProtocol = iInteractiveProtocol;
        }
    }

    public boolean isYandexNaviInstalled() {
        return this.isYandexNaviInstalled;
    }

    // Метод для внешнего управления состоянием навигации
    public void updateNaviState() {
        // Здесь можно добавить логику для определения состояния Яндекс.Навигатора
        // Например, через проверку активных процессов или другие методы
        HandlerUtil.getThreadHandler().post(new Runnable() {
            @Override
            public void run() {
                // Пока используем простую логику - если приложение установлено,
                // считаем что навигация может быть активна
                if (isYandexNaviInstalled) {
                    // Можно добавить дополнительные проверки здесь
                }
            }
        });
    }
}
