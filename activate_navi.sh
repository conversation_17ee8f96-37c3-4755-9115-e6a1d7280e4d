#!/bin/bash

# Скрипт для активации/деактивации режима навигации на приборной панели
# Использование: ./activate_navi.sh [on|off]

if [ "$#" -ne 1 ]; then
    echo "Использование: $0 [on|off]"
    echo "  on  - активировать режим навигации (включить трансляцию)"
    echo "  off - деактивировать режим навигации (выключить трансляцию)"
    exit 1
fi

case "$1" in
    "on")
        echo "🚀 Активация режима навигации..."
        adb shell am broadcast -a com.chinatsp.dashboard.FORCE_NAVI_ACTIVE --ez navi_state true
        echo "✅ Команда отправлена: режим навигации ВКЛЮЧЕН"
        ;;
    "off")
        echo "🛑 Деактивация режима навигации..."
        adb shell am broadcast -a com.chinatsp.dashboard.FORCE_NAVI_ACTIVE --ez navi_state false
        echo "✅ Команда отправлена: режим навигации ВЫКЛЮЧЕН"
        ;;
    *)
        echo "❌ Неверный параметр: $1"
        echo "Используйте 'on' или 'off'"
        exit 1
        ;;
esac

echo ""
echo "📱 Проверьте приборную панель - она должна переключиться в соответствующий режим"
echo "📋 Для просмотра логов: adb logcat | grep -E '(YandexNaviModel|DashboardModel|InteractiveModel)'"
