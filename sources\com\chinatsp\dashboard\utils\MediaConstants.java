package com.chinatsp.dashboard.utils;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/* loaded from: classes.dex */
public class MediaConstants {
    public static final String ACTION_SHARE_PRODUCT_TTS = "chinatsp.action.virtual.tts";
    public static final String FUEL_SHARE_PATH = "fuel_share_path";
    public static final String FUEL_SHARE_VALUE = "fuel_share_value";
    public static final String KEY_SHARE_TTS = "extra";
    public static final String MILEAGE_SHARE_PATH = "mileage_share_path";
    public static final String MILEAGE_SHARE_VALUE = "mileage_share_value";
    public static final String PACKAGE_NAVI = "ru.yandex.yandexnavi";
    public static final String PACKAGE_SYSTEM_UI = "com.android.systemui";
    public static final String SETTINGS_KEY_LIGHT_SHOW_INDEX = "key_light_show_index";
    public static final String SETTING_KEY_METER_PAGE_STATE = "key_meter_page_state";
    public static final int SETTING_VALUE_METER_PAGE_COMFORT = 0;
    public static final int SETTING_VALUE_METER_PAGE_IACC = 4;
    public static final int SETTING_VALUE_METER_PAGE_NAVI = 2;
    public static final int SETTING_VALUE_METER_PAGE_OTHER = 5;
    public static final int SETTING_VALUE_METER_PAGE_SPORT = 1;
    public static final int SETTING_VALUE_METER_PAGE_TRACK = 3;
    public static final String VALUE_SHARE_FUEL = "EXTRA_SHARE_OIL";
    public static final String VALUE_SHARE_MILEAGE = "EXTRA_SHARE_MILEAGE";
    public static final String VOICE_ACTION_SWITCH_NAVI = "voice_action_switch_navi";
    public static final String VOICE_KEY_SWITCH = "key_switch";
    public static final String VOICE_KEY_TYPE_DISPLAY = "key_type_display";
    public static final int VOICE_VALUE_DISPLAY_METER = 1;
    public static final int VOICE_VALUE_STATE_OFF = 2;
    public static final int VOICE_VALUE_STATE_ON = 1;
    public static final String WHEEL_ACTION_TOUCH = "com.coagent.intent.action.KEY_CHANGED";
    public static final String WHEEL_KEY_CODE = "Key_code";
    public static final String WHEEL_KEY_STATE = "Key_state";
    public static final String WHEEL_VALUE_CODE_TURN_PAGE = "TURN_PAGE";
    public static final String WHEEL_VALUE_STATE_DOWN = "DOWN";
    public static final String WHEEL_VALUE_STATE_LONG_EVENT = "LONG_EVENT";
    public static final String WHEEL_VALUE_STATE_LONG_UP = "LONG_UP";
    public static final String WHEEL_VALUE_STATE_UP = "UP";

    public static class AnimationType {
        public static final int CLOSE_DOOR = 4;
        public static final int FACE_DIMISSING = 11;
        public static final int FACE_SCANNING = 10;
        public static final int FACE_WAITING = 9;
        public static final int FIRE_FLAMEOUT = 6;
        public static final int FIRE_IGNITION = 5;
        public static final int KEY_OR_OPEN = 2;
        public static final int OPEN_DOOR = 3;
        public static final int OPEN_DOOR_MAIN = 7;
        public static final int POUR_OIL = 8;
        public static final int POWER_OFF = 1;
        public static final int POWER_ON = 0;
        public static final int THEME_COMFORT_ADMISSION = 17;
        public static final int THEME_CUTTO = 13;
        public static final int THEME_ENTRANCE = 12;
        public static final int THEME_EXIT_COMFORT = 19;
        public static final int THEME_EXIT_SPORT = 18;
        public static final int THEME_FITNESS = 21;
        public static final int THEME_LIGHT = 22;
        public static final int THEME_OFFTER_BG = 15;
        public static final int THEME_SIMPLE_NAVI = 24;
        public static final int THEME_SLEEP_MODE = 20;
        public static final int THEME_SPORT_ADMISSION = 16;
        public static final int THEME_TRACK = 23;
        public static final int THEME_YINGBING = 14;
    }

    public static class DisPlayType {
        public static final int DISPLAY_TYPE_THREE = 2;
        public static final int DISPLAY_TYPE_YIBIAO = 1;
    }

    public static class DriverType {
        public static final int TYPE_DRIVER_DAY = 7;
        public static final int TYPE_DRIVER_DETAULT = 4;
        public static final int TYPE_DRIVER_DISMISS = 5;
        public static final int TYPE_DRIVER_FULL_NAVI = 6;
        public static final int TYPE_DRIVER_FULL_NAVI_DAYTIME = 10;
        public static final int TYPE_DRIVER_IACC = 2;
        public static final int TYPE_DRIVER_INIT = 9;
        public static final int TYPE_DRIVER_NIGHT = 8;
        public static final int TYPE_DRIVER_ORDINARY = 0;
        public static final int TYPE_DRIVER_SIMPLE_NAVI_DAYTIME = 11;
        public static final int TYPE_DRIVER_SIMPLE_NAVI_NIGHT = 12;
        public static final int TYPE_DRIVER_SPORTS = 1;
        public static final int TYPE_DRIVER_TRACK = 3;
    }

    public static class MediaType {
        public static final int TYPE_AM_RADIO = 10;
        public static final int TYPE_BABY_SING = 9;
        public static final int TYPE_BT_MUSIC = 2;
        public static final int TYPE_CHANGBA = 7;
        public static final int TYPE_INVALID = -1;
        public static final int TYPE_KUGOU_CC = 8;
        public static final int TYPE_KUGOU_MUSIC = 3;
        public static final int TYPE_LOCAL_RADIO = 5;
        public static final int TYPE_QQ_MUSIC = 0;
        public static final int TYPE_USB_MUSIC = 1;
        public static final int TYPE_WECHAT_MUSIC = 4;
        public static final int TYPE_XMLY_RADIO = 6;
    }

    public static class PlayState {
        public static final int PAUSE = 2;
        public static final int PLAY = 1;
        public static final int PRE_PLAY = 0;
        public static final int STOP = 3;
    }

    @Retention(RetentionPolicy.SOURCE)
    public @interface RequestValue {
        public static final int ACC = 2;
        public static final int AI_LIGHT_ENTER = 28;
        public static final int AI_LIGHT_EXIT = 29;
        public static final int ASSISTANCE_ENTER = 21;
        public static final int ASSISTANCE_EXIT = 20;
        public static final int BOOT_UP = 6;
        public static final int EXIT_SIMPLE_NAVI = 5;
        public static final int FACE_RECOGNITION_ENTER = 26;
        public static final int FACE_RECOGNITION_EXIT = 27;
        public static final int FULL_NAVI = 4;
        public static final int FULL_NAVI_DAYTIME = 18;
        public static final int GAME_FITNESS_ENTER = 16;
        public static final int GAME_FITNESS_EXIT = 17;
        public static final int IACC = 1;
        public static final int INVALID = -1;
        public static final int MEDIA_EXIT = 19;
        public static final int REST_MODE_ENTER = 22;
        public static final int REST_MODE_EXIT = 23;
        public static final int SHUT_DOWN = 7;
        public static final int SIMPLE_NAVI_DAY = 25;
        public static final int SIMPLE_NAVI_NIGHT = 24;
        public static final int THEME_COMFORT = 8;
        public static final int THEME_SIMPLE_AUTO = 13;
        public static final int THEME_SIMPLE_DAY = 10;
        public static final int THEME_SIMPLE_NIGHT = 11;
        public static final int THEME_SPORT = 9;
        public static final int TRACK = 3;
        public static final int TRACk_GAME_ENTER = 30;
        public static final int TRACk_GAME_EXIT = 31;
        public static final int WELCOME = 14;
    }

    @Retention(RetentionPolicy.SOURCE)
    public @interface SettingStateValue {
    }

    public static class SourceID {
        public static final String BTAUDIO = "BTAUDIO";
        public static final String HDD = "HDD";
        public static final String KUGOU_MUSIC = "KUGOU_MUSIC";
        public static final String QQ_MUSIC = "QQ_MUSIC";
        public static final String SOURCE_KNOWN = "source_known";
        public static final String TUNER = "TUNER";
        public static final String USB = "USB";
        public static final String WECHAT_MUSIC = "WECHAT_MUSIC";
        public static final String XIMALAYA = "XIMALAYA";
    }

    @Retention(RetentionPolicy.SOURCE)
    public @interface ThemeValue {
        public static final int THEME_COMFORT = 1;
        public static final int THEME_DAY = 3;
        public static final int THEME_FULL_NAVI = 7;
        public static final int THEME_FULL_NAVI_DAYTIME = 8;
        public static final int THEME_IACC = 4;
        public static final int THEME_INVALID = -1;
        public static final int THEME_NIGHT = 5;
        public static final int THEME_SIMPLE_NAVI_DAYTIME = 10;
        public static final int THEME_SIMPLE_NAVI_NIGHT = 9;
        public static final int THEME_SPORT = 2;
        public static final int THEME_TRACK = 6;
    }
}
