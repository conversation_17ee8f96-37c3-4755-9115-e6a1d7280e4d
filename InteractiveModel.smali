.class public Lcom/chinatsp/dashboard/model/InteractiveModel;
.super Ljava/lang/Object;
.source "InteractiveModel.java"

# interfaces
.implements Lcom/chinatsp/dashboard/model/IInteractiveProtocol;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;,
        Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;,
        Lcom/chinatsp/dashboard/model/InteractiveModel$IProtocol;
    }
.end annotation


# static fields
.field public static final CURRENT_SPLIT_SCREEN_MODE:Ljava/lang/String; = "key_multi_window_state"

.field private static final NAVI_PAGE_CHANGE_KEY:Ljava/lang/String; = "com.chinatsp.systemui.NAVI_PAGE"

.field public static final PACKAGE_FITNESS:Ljava/lang/String; = "com.Oshan.Fitness"

.field public static final PACKAGE_SETTINGS:Ljava/lang/String; = "com.chinatsp.settings"

.field private static final PAGE_CHANGE_DELAY:I = 0x190

.field private static final PKG_AIR_CONDITIONER:Ljava/lang/String; = "com.os.airconditioner"

.field private static final PKG_CURR_TOP_KEY:Ljava/lang/String; = "com.chinatsp.systemui.cur_package"

.field private static final PKG_NAVI:Ljava/lang/String; = "ru.yandex.yandexnavi"

.field private static final PKG_SETTING:Ljava/lang/String; = "com.chinatsp.settings"

.field public static final SCREEN_CLOCK_STATUE:Ljava/lang/String; = "screen_clock"

.field public static final SCREEN_STATUE_IN_CLOCK:I = 0x1

.field public static final SCREEN_STATUE_NORMAL:I = 0x0

.field public static final SHOW_MODE:Ljava/lang/String; = "show_mode"

.field public static final SPLIT_MODE_FULL_SCREEN:I = 0x0

.field public static final SPLIT_MODE_SPLIT_SCREEN:I = 0x1

.field private static final SYSTEM_NOTIFY_CLOSE_SPLIT:Ljava/lang/String; = "system_notify_close_split"

.field private static final TAG:Ljava/lang/String; = "InteractiveModel"


# instance fields
.field private carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

.field private final clearCarServiceTask:Ljava/lang/Runnable;

.field private final context:Landroid/content/Context;

.field private currentTopPackage:Ljava/lang/String;

.field private jumpToNavi2ByAvm:Z

.field private lastAvmDisplayShow:Z

.field private lastTopPackage:Ljava/lang/String;

.field private volatile naviMultiScreenState:Z

.field private naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

.field private final pageChangeObserver:Landroid/database/ContentObserver;

.field private final topAppChangeTask:Ljava/lang/Runnable;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 2

    .line 209
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const-string v0, ""

    .line 110
    iput-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->lastTopPackage:Ljava/lang/String;

    .line 114
    iput-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->currentTopPackage:Ljava/lang/String;

    const/4 v0, 0x1

    .line 121
    iput-boolean v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviMultiScreenState:Z

    .line 135
    new-instance v0, Lcom/chinatsp/dashboard/model/-$$Lambda$InteractiveModel$7mHRIbKgC0axCq1eL5uNSuFerWI;

    invoke-direct {v0, p0}, Lcom/chinatsp/dashboard/model/-$$Lambda$InteractiveModel$7mHRIbKgC0axCq1eL5uNSuFerWI;-><init>(Lcom/chinatsp/dashboard/model/InteractiveModel;)V

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->topAppChangeTask:Ljava/lang/Runnable;

    .line 146
    new-instance v0, Lcom/chinatsp/dashboard/model/-$$Lambda$InteractiveModel$yW4IvBt2h1MpDrtCd5Mt8_tS7e8;

    invoke-direct {v0, p0}, Lcom/chinatsp/dashboard/model/-$$Lambda$InteractiveModel$yW4IvBt2h1MpDrtCd5Mt8_tS7e8;-><init>(Lcom/chinatsp/dashboard/model/InteractiveModel;)V

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->clearCarServiceTask:Ljava/lang/Runnable;

    .line 155
    new-instance v0, Lcom/chinatsp/dashboard/model/InteractiveModel$1;

    invoke-static {}, Lcom/chinatsp/dashboard/utils/HandlerUtil;->getThreadHandler()Landroid/os/Handler;

    move-result-object v1

    invoke-direct {v0, p0, v1}, Lcom/chinatsp/dashboard/model/InteractiveModel$1;-><init>(Lcom/chinatsp/dashboard/model/InteractiveModel;Landroid/os/Handler;)V

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->pageChangeObserver:Landroid/database/ContentObserver;

    const/4 v0, 0x0

    .line 403
    iput-boolean v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z

    .line 408
    iput-boolean v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->lastAvmDisplayShow:Z

    .line 210
    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    return-void
.end method

.method static synthetic access$000(Lcom/chinatsp/dashboard/model/InteractiveModel;)Z
    .locals 0

    .line 42
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->isSplitScreenMode()Z

    move-result p0

    return p0
.end method

.method static synthetic access$100(Lcom/chinatsp/dashboard/model/InteractiveModel;)Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;
    .locals 0

    .line 42
    iget-object p0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    return-object p0
.end method

.method private exitOtherNavi()V
    .locals 2

    const-string v0, "InteractiveModel"

    const-string v1, "exitOtherNavi"

    .line 328
    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 329
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isMeterNaviRunning()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 330
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    const/4 v1, 0x0

    invoke-interface {v0, v1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->requestMeterFullNavi(Z)V

    :cond_0
    return-void
.end method

.method private getTopAppPkg()Ljava/lang/String;
    .locals 3

    .line 219
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    const-string v1, "com.chinatsp.systemui.cur_package"

    invoke-static {v0, v1}, Landroid/provider/Settings$System;->getString(Landroid/content/ContentResolver;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    .line 220
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "getTopAppPkg pkg:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    return-object v0
.end method

.method private isScreenClockMode()Z
    .locals 4

    .line 246
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    const-string v1, "screen_clock"

    const/4 v2, 0x0

    invoke-static {v0, v1, v2}, Landroid/provider/Settings$System;->getInt(Landroid/content/ContentResolver;Ljava/lang/String;I)I

    move-result v0

    .line 247
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getSplitScreenMode screenClock:"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    move v2, v1

    :cond_0
    return v2
.end method

.method private isSplitScreenMode()Z
    .locals 4

    .line 237
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    const-string v1, "key_multi_window_state"

    const/4 v2, 0x0

    invoke-static {v0, v1, v2}, Landroid/provider/Settings$System;->getInt(Landroid/content/ContentResolver;Ljava/lang/String;I)I

    move-result v0

    .line 238
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getSplitScreenMode screenMode:"

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    move v2, v1

    :cond_0
    return v2
.end method

.method private startFunctionDesk()V
    .locals 4

    .line 676
    invoke-static {}, Lcom/chinatsp/dashboard/utils/Logcat;->d()V

    .line 677
    new-instance v0, Landroid/content/Intent;

    invoke-direct {v0}, Landroid/content/Intent;-><init>()V

    const/high16 v1, 0x10000000

    .line 678
    invoke-virtual {v0, v1}, Landroid/content/Intent;->addFlags(I)Landroid/content/Intent;

    .line 679
    new-instance v1, Landroid/content/ComponentName;

    const-string v2, "com.chinatsp.launcher"

    const-string v3, "com.chinatsp.launcher.MainActivity"

    invoke-direct {v1, v2, v3}, Landroid/content/ComponentName;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    .line 681
    invoke-virtual {v0, v1}, Landroid/content/Intent;->setComponent(Landroid/content/ComponentName;)Landroid/content/Intent;

    .line 682
    iget-object v1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {v1, v0}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    return-void
.end method

.method private topAppChange(Ljava/lang/String;)V
    .locals 4

    .line 256
    invoke-direct {p0, p1}, Lcom/chinatsp/dashboard/model/InteractiveModel;->updateYandexNaviState(Ljava/lang/String;)V

    .line 257
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    if-nez v0, :cond_0

    .line 258
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "topAppChange fail. carServiceProtocol is null. pkg:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "InteractiveModel"

    invoke-static {v0, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 261
    :cond_0
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

    invoke-interface {v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;->isNaviRunning()Z

    move-result v0

    if-nez v0, :cond_1

    return-void

    .line 265
    :cond_1
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->isScreenClockMode()Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_3

    const-string p1, "in screenClock mode"

    .line 266
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 267
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isMeterNaviRunning()Z

    move-result p1

    if-nez p1, :cond_2

    .line 268
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isRestMode()Z

    move-result p1

    if-nez p1, :cond_2

    const-string p1, "in screenClock mode requestMeterNavi"

    .line 269
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 270
    invoke-virtual {p0, v1}, Lcom/chinatsp/dashboard/model/InteractiveModel;->requestMeterNavi(Z)V

    :cond_2
    return-void

    .line 275
    :cond_3
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "topAppChange pkg="

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 277
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->isSplitScreenMode()Z

    move-result v0

    const/4 v2, 0x0

    const-string v3, "request meter navi exit"

    if-nez v0, :cond_7

    const-string v0, "com.android.systemui"

    invoke-static {v0, p1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_4

    goto :goto_0

    :cond_4
    const-string v0, "out Split Screen Mode"

    .line 287
    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    const-string v0, "ru.yandex.yandexnavi"

    .line 289
    invoke-static {v0, p1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_5

    .line 291
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isMeterNaviRunning()Z

    move-result p1

    if-nez p1, :cond_8

    const-string p1, "request meter navi"

    .line 292
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 298
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isRestMode()Z

    move-result p1

    if-nez p1, :cond_8

    .line 299
    invoke-virtual {p0, v1}, Lcom/chinatsp/dashboard/model/InteractiveModel;->requestMeterNavi(Z)V

    goto :goto_1

    .line 303
    :cond_5
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isMeterNaviRunning()Z

    move-result p1

    if-eqz p1, :cond_8

    .line 304
    invoke-static {v3}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 306
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isAvmOn()Z

    move-result p1

    if-eqz p1, :cond_6

    return-void

    .line 309
    :cond_6
    invoke-virtual {p0, v2}, Lcom/chinatsp/dashboard/model/InteractiveModel;->requestMeterNavi(Z)V

    .line 310
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isRestMode()Z

    move-result p1

    if-nez p1, :cond_8

    .line 311
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->exitFullNaviPresentation()V

    goto :goto_1

    .line 278
    :cond_7
    :goto_0
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isMeterNaviRunning()Z

    move-result p1

    if-eqz p1, :cond_8

    .line 279
    invoke-static {v3}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 280
    invoke-virtual {p0, v2}, Lcom/chinatsp/dashboard/model/InteractiveModel;->requestMeterNavi(Z)V

    .line 281
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isRestMode()Z

    move-result p1

    if-nez p1, :cond_8

    .line 282
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->exitFullNaviPresentation()V

    :cond_8
    :goto_1
    return-void
.end method


# virtual methods
.method public avmDisplaySwitch(Z)V
    .locals 8

    .line 428
    :try_start_0
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const-string v1, "InteractiveModel"

    if-nez v0, :cond_0

    .line 429
    :try_start_1
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "avmDisplaySwitch fail. naviProtocol is null. isShow:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 509
    iput-boolean p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->lastAvmDisplayShow:Z

    return-void

    .line 432
    :cond_0
    :try_start_2
    iget-object v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    if-nez v2, :cond_1

    .line 509
    iput-boolean p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->lastAvmDisplayShow:Z

    return-void

    .line 435
    :cond_1
    :try_start_3
    invoke-interface {v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;->isNaviRunning()Z

    move-result v0

    if-nez v0, :cond_2

    const-string v0, "avmDisplaySwitch not response. navi is not running."

    .line 436
    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 437
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->exitOtherNavi()V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 509
    iput-boolean p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->lastAvmDisplayShow:Z

    return-void

    .line 440
    :cond_2
    :try_start_4
    invoke-virtual {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->isNaviTop()Z

    move-result v0

    .line 442
    iget-object v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {v2}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isMeterNaviRunning()Z

    move-result v2
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    const/4 v3, 0x1

    const-string v4, " , jumpToNavi2ByAvm:"

    const-string v5, "avmDisplaySwitch reset jumpToNavi2ByAvm"

    const/4 v6, 0x0

    if-eqz v2, :cond_8

    .line 443
    :try_start_5
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "avmDisplaySwitch meter navi is running. isShow:"

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-boolean v4, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    if-eqz p1, :cond_4

    if-eqz v0, :cond_3

    .line 449
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->startFunctionDesk()V

    .line 450
    iput-boolean v3, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    .line 509
    iput-boolean p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->lastAvmDisplayShow:Z

    return-void

    :cond_3
    :try_start_6
    const-string v0, "avmDisplaySwitch  meter navi is running. navi is\u2019t top"

    .line 454
    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const-string v0, "avmDisplaySwitch not response. meter navi is running."

    .line 461
    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_1

    .line 464
    :cond_4
    iget-boolean v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z

    if-eqz v2, :cond_6

    if-eqz v0, :cond_5

    .line 468
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {v0, v6}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->requestMeterFullNavi(Z)V

    goto :goto_0

    .line 470
    :cond_5
    invoke-virtual {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->startNaviDesk()V

    .line 472
    :goto_0
    iput-boolean v6, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z

    .line 473
    invoke-static {v1, v5}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_1

    :cond_6
    if-eqz v0, :cond_7

    .line 477
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {v0, v6}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->requestMeterFullNavi(Z)V

    :cond_7
    const-string v0, " jumpToNavi2ByAvm = false , don\u2018t do anything."

    .line 479
    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_1

    .line 485
    :cond_8
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "avmDisplaySwitch response. isShow:"

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-boolean v4, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    if-eqz p1, :cond_a

    if-eqz v0, :cond_9

    .line 490
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->startFunctionDesk()V

    .line 491
    iput-boolean v3, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z

    goto :goto_1

    .line 494
    :cond_9
    iput-boolean v6, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z

    .line 495
    invoke-static {v1, v5}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_1

    .line 499
    :cond_a
    iget-boolean v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z

    if-eqz v0, :cond_b

    .line 501
    invoke-virtual {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->startNaviDesk()V

    const-string v0, "avmDisplaySwitch reset startNaviDesk"

    .line 502
    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 504
    :cond_b
    invoke-static {v1, v5}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 505
    iput-boolean v6, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_0

    .line 509
    :goto_1
    iput-boolean p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->lastAvmDisplayShow:Z

    return-void

    :catchall_0
    move-exception v0

    iput-boolean p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->lastAvmDisplayShow:Z

    .line 510
    throw v0
.end method

.method public exitSimpleNavi()V
    .locals 0

    return-void
.end method

.method public isJumpToNavi2ByAvm()Z
    .locals 1

    .line 412
    iget-boolean v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z

    return v0
.end method

.method public isNaviRunning()Z
    .locals 2

    .line 703
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

    if-nez v0, :cond_0

    const-string v0, "InteractiveModel"

    const-string v1, "naviProtocol is null."

    .line 704
    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v0, 0x0

    return v0

    .line 707
    :cond_0
    invoke-interface {v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;->isNaviRunning()Z

    move-result v0

    return v0
.end method

.method public isNaviTop()Z
    .locals 3

    .line 228
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->getTopAppPkg()Ljava/lang/String;

    move-result-object v0

    const-string v1, "ru.yandex.yandexnavi"

    invoke-static {v1, v0}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result v0

    .line 229
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "isNaviTop result: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    return v0
.end method

.method public synthetic lambda$new$0$InteractiveModel()V
    .locals 3

    .line 136
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->getTopAppPkg()Ljava/lang/String;

    move-result-object v0

    .line 137
    iget-object v1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->currentTopPackage:Ljava/lang/String;

    iput-object v1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->lastTopPackage:Ljava/lang/String;

    .line 138
    iput-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->currentTopPackage:Ljava/lang/String;

    .line 139
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "pageChangeObserver onChange topApp:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "InteractiveModel"

    invoke-static {v2, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 140
    invoke-direct {p0, v0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->topAppChange(Ljava/lang/String;)V

    return-void
.end method

.method public synthetic lambda$new$1$InteractiveModel()V
    .locals 1

    .line 147
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    if-eqz v0, :cond_0

    .line 148
    invoke-interface {v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->removeFullNaviChangeTask()V

    :cond_0
    return-void
.end method

.method public meterFullNaviRequest(Z)Z
    .locals 4

    .line 373
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

    const-string v1, "InteractiveModel"

    const/4 v2, 0x0

    if-nez v0, :cond_0

    .line 374
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "meterFullNaviRequest fail. naviProtocol is null. isEnterOrExit:"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v2

    .line 377
    :cond_0
    iget-object v3, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    if-nez v3, :cond_1

    .line 378
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "meterFullNaviRequest fail. carServiceProtocol is null. isEnterOrExit:"

    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v2

    .line 381
    :cond_1
    invoke-interface {v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;->isNaviRunning()Z

    move-result v0

    if-nez v0, :cond_2

    const-string p1, "meterFullNaviRequest fail. navi is not running."

    .line 383
    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 384
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->exitOtherNavi()V

    return v2

    .line 388
    :cond_2
    iget-object v3, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {v3}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isTrackRunning()Z

    move-result v3

    if-eqz v3, :cond_3

    return v2

    .line 391
    :cond_3
    iget-object v3, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {v3}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->isRestMode()Z

    move-result v3

    if-eqz v3, :cond_4

    return v2

    .line 394
    :cond_4
    iget-object v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {v2, p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->requestMeterFullNavi(Z)V

    .line 395
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "meterFullNaviRequest isNaviRunning:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v2, " , isEnterOrExit:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 p1, 0x1

    return p1
.end method

.method public meterNaviStateChange(Z)V
    .locals 4

    .line 607
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

    const-string v1, "InteractiveModel"

    if-nez v0, :cond_0

    .line 608
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "meterNaviStateChange fail. naviProtocol is null. isShowOrHide:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 611
    :cond_0
    iget-object v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    if-nez v2, :cond_1

    .line 612
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "meterNaviStateChange fail. carServiceProtocol is null. isShowOrHide:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 615
    :cond_1
    invoke-interface {v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;->isNaviRunning()Z

    move-result v0

    if-nez v0, :cond_2

    const-string p1, "meterNaviStateChange not response. navi is not running."

    .line 616
    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 617
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->exitOtherNavi()V

    return-void

    .line 629
    :cond_2
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "meterNaviStateChange isShowOrHide="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 630
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->getTopAppPkg()Ljava/lang/String;

    move-result-object v0

    const-string v1, "ru.yandex.yandexnavi"

    if-eqz p1, :cond_5

    .line 633
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->isSplitScreenMode()Z

    move-result p1

    if-eqz p1, :cond_4

    .line 634
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {p1}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object p1

    const-string v0, "system_notify_close_split"

    const/4 v1, 0x2

    invoke-static {p1, v0, v1}, Landroid/provider/Settings$Global;->getInt(Landroid/content/ContentResolver;Ljava/lang/String;I)I

    move-result p1

    .line 635
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, " in Split Screen isOutRightSplit="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    if-ne p1, v1, :cond_3

    .line 637
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {p1}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object p1

    const/4 v2, 0x0

    invoke-static {p1, v0, v2}, Landroid/provider/Settings$Global;->putInt(Landroid/content/ContentResolver;Ljava/lang/String;I)Z

    .line 639
    :cond_3
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {p1}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object p1

    invoke-static {p1, v0, v1}, Landroid/provider/Settings$Global;->putInt(Landroid/content/ContentResolver;Ljava/lang/String;I)Z

    return-void

    .line 644
    :cond_4
    invoke-static {v1, v0}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_6

    const-string p1, "startFunctionDesk"

    .line 645
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 646
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->startFunctionDesk()V

    goto :goto_0

    :cond_5
    const-string p1, "startNaviDesk1"

    .line 655
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 657
    invoke-static {v1, v0}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-nez p1, :cond_6

    const-string p1, "startNaviDesk from navi"

    .line 658
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 659
    invoke-virtual {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->startNaviDesk()V

    :cond_6
    :goto_0
    return-void
.end method

.method public naviStateChange(Z)V
    .locals 3

    .line 521
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "naviStateChange isNavi:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "InteractiveModel"

    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 522
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

    if-nez v0, :cond_0

    .line 523
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "naviStateChange fail. naviProtocol is null. isNavi:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 526
    :cond_0
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    if-nez v0, :cond_1

    .line 527
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "naviStateChange fail. carServiceProtocol is null. isNavi:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 531
    :cond_1
    iget-object v1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-static {v1}, Lcom/chinatsp/dashboard/utils/ThemeChangeHelp;->isNight(Landroid/content/Context;)Z

    move-result v1

    invoke-interface {v0, p1, v1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->naviStateChanged(ZZ)V

    return-void
.end method

.method public receiveMeterStateChange(Z)V
    .locals 3

    .line 578
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

    const-string v1, "InteractiveModel"

    if-nez v0, :cond_0

    .line 579
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "receiveMeterNaviStateChange fail. naviProtocol is null. isShowOrHide:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 582
    :cond_0
    iget-object v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    if-nez v2, :cond_1

    .line 583
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "receiveMeterNaviStateChange fail. carServiceProtocol is null. isShowOrHide:"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 586
    :cond_1
    invoke-interface {v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;->isNaviRunning()Z

    move-result p1

    if-nez p1, :cond_2

    const-string p1, "receiveMeterNaviStateChange not response. navi is not running."

    .line 587
    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 588
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->exitOtherNavi()V

    return-void

    .line 591
    :cond_2
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->isScreenClockMode()Z

    move-result p1

    if-eqz p1, :cond_3

    const-string p1, "receive meter navi state change is in screen clock mode."

    .line 593
    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 596
    :cond_3
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->getTopAppPkg()Ljava/lang/String;

    move-result-object p1

    const-string v0, "ru.yandex.yandexnavi"

    .line 597
    invoke-static {v0, p1}, Landroid/text/TextUtils;->equals(Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Z

    move-result p1

    if-eqz p1, :cond_4

    const-string p1, "startFunctionDesk"

    .line 598
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 599
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    const/4 v0, 0x0

    invoke-interface {p1, v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->requestMeterFullNavi(Z)V

    .line 600
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->exitFullNaviPresentation()V

    :cond_4
    return-void
.end method

.method public register()V
    .locals 4

    .line 339
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    const-string v1, "com.chinatsp.systemui.cur_package"

    invoke-static {v1}, Landroid/provider/Settings$System;->getUriFor(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v1

    iget-object v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->pageChangeObserver:Landroid/database/ContentObserver;

    const/4 v3, 0x0

    invoke-virtual {v0, v1, v3, v2}, Landroid/content/ContentResolver;->registerContentObserver(Landroid/net/Uri;ZLandroid/database/ContentObserver;)V

    .line 340
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    const-string v1, "key_multi_window_state"

    invoke-static {v1}, Landroid/provider/Settings$System;->getUriFor(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v1

    iget-object v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->pageChangeObserver:Landroid/database/ContentObserver;

    invoke-virtual {v0, v1, v3, v2}, Landroid/content/ContentResolver;->registerContentObserver(Landroid/net/Uri;ZLandroid/database/ContentObserver;)V

    .line 341
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    const-string v1, "screen_clock"

    invoke-static {v1}, Landroid/provider/Settings$System;->getUriFor(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v1

    iget-object v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->pageChangeObserver:Landroid/database/ContentObserver;

    invoke-virtual {v0, v1, v3, v2}, Landroid/content/ContentResolver;->registerContentObserver(Landroid/net/Uri;ZLandroid/database/ContentObserver;)V

    .line 342
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    const-string v1, "show_mode"

    invoke-static {v1}, Landroid/provider/Settings$System;->getUriFor(Ljava/lang/String;)Landroid/net/Uri;

    move-result-object v1

    iget-object v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->pageChangeObserver:Landroid/database/ContentObserver;

    invoke-virtual {v0, v1, v3, v2}, Landroid/content/ContentResolver;->registerContentObserver(Landroid/net/Uri;ZLandroid/database/ContentObserver;)V

    return-void
.end method

.method public requestMeterNavi(Z)V
    .locals 3

    .line 559
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "requestMeterNavi isShowOrHide:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "InteractiveModel"

    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 560
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

    if-nez v0, :cond_0

    const-string p1, "requestMeterNavi fail. naviProtocol is null."

    .line 561
    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 564
    :cond_0
    iget-object v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    if-nez v2, :cond_1

    const-string p1, "requestMeterNavi fail. carServiceProtocol is null."

    .line 565
    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 568
    :cond_1
    invoke-interface {v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;->isNaviRunning()Z

    move-result v0

    if-nez v0, :cond_2

    const-string p1, "requestMeterNavi not response. navi is not running."

    .line 569
    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 570
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->exitOtherNavi()V

    return-void

    .line 573
    :cond_2
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {v0, p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->requestMeterFullNavi(Z)V

    return-void
.end method

.method public requestMeterSimpleNavi(Z)V
    .locals 3

    .line 540
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "requestMeterSimpleNavi isShowOrHide:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "InteractiveModel"

    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 541
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

    if-nez v0, :cond_0

    const-string p1, "requestMeterSimpleNavi fail. naviProtocol is null."

    .line 542
    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 545
    :cond_0
    iget-object v2, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    if-nez v2, :cond_1

    const-string p1, "requestMeterSimpleNavi fail. carServiceProtocol is null."

    .line 546
    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 549
    :cond_1
    invoke-interface {v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;->isNaviRunning()Z

    move-result v0

    if-nez v0, :cond_2

    const-string p1, "requestMeterSimpleNavi not response. navi is not running."

    .line 550
    invoke-static {v1, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 551
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->exitOtherNavi()V

    return-void

    .line 554
    :cond_2
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    invoke-interface {v0, p1}, Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;->requestMeterSimpleNavi(Z)V

    return-void
.end method

.method public runTopAppChangTask()V
    .locals 4

    .line 201
    invoke-static {}, Lcom/chinatsp/dashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->topAppChangeTask:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V

    .line 202
    invoke-virtual {p0}, Lcom/chinatsp/dashboard/model/InteractiveModel;->isNaviTop()Z

    move-result v0

    if-eqz v0, :cond_0

    .line 204
    invoke-static {}, Lcom/chinatsp/dashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->topAppChangeTask:Ljava/lang/Runnable;

    const-wide/16 v2, 0xfa

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 206
    :cond_0
    invoke-static {}, Lcom/chinatsp/dashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->topAppChangeTask:Ljava/lang/Runnable;

    const-wide/16 v2, 0x190

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void
.end method

.method public setCarServiceProtocol(Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;)V
    .locals 2

    .line 366
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "setCarServiceProtocol carServiceProtocol:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "InteractiveModel"

    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 367
    iput-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->carServiceProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$ICarServiceProtocol;

    return-void
.end method

.method public setJumpToNavi2ByAvm(Z)V
    .locals 2

    .line 416
    iput-boolean p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->jumpToNavi2ByAvm:Z

    .line 417
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "avmDisplaySwitch reset jumpToNavi2ByAvm = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "InteractiveModel"

    invoke-static {v0, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public setNaviProtocol(Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;)V
    .locals 2

    .line 357
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "setNaviProtocol naviProtocol:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "InteractiveModel"

    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 358
    iput-object p1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

    return-void
.end method

.method public startNaviDesk()V
    .locals 2

    const-string v0, "startNaviDesk"

    .line 665
    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 666
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getPackageManager()Landroid/content/pm/PackageManager;

    move-result-object v0

    const-string v1, "ru.yandex.yandexnavi"

    .line 667
    invoke-virtual {v0, v1}, Landroid/content/pm/PackageManager;->getLaunchIntentForPackage(Ljava/lang/String;)Landroid/content/Intent;

    move-result-object v0

    const-string v1, "android.intent.category.DEFAULT"

    .line 668
    invoke-virtual {v0, v1}, Landroid/content/Intent;->addCategory(Ljava/lang/String;)Landroid/content/Intent;

    .line 669
    iget-object v1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {v1, v0}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    return-void
.end method

.method public unregister()V
    .locals 2

    .line 349
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->context:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    iget-object v1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->pageChangeObserver:Landroid/database/ContentObserver;

    invoke-virtual {v0, v1}, Landroid/content/ContentResolver;->unregisterContentObserver(Landroid/database/ContentObserver;)V

    return-void
.end method

.method public upDataNaviShow()V
    .locals 2

    .line 687
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

    invoke-interface {v0}, Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;->isNaviRunning()Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, "InteractiveModel"

    const-string v1, "upDataNaviShow not response. navi is not running."

    .line 688
    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void

    .line 691
    :cond_0
    invoke-static {}, Lcom/chinatsp/dashboard/utils/HandlerUtil;->getMainHandler()Landroid/os/Handler;

    move-result-object v0

    iget-object v1, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->clearCarServiceTask:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    return-void
.end method

.method private updateYandexNaviState(Ljava/lang/String;)V
    .locals 3

    .line 700
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/InteractiveModel;->naviProtocol:Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;

    instance-of v1, v0, Lcom/chinatsp/dashboard/model/YandexNaviModel;

    if-eqz v1, :cond_1

    .line 701
    check-cast v0, Lcom/chinatsp/dashboard/model/YandexNaviModel;

    .line 702
    const-string v1, "ru.yandex.yandexnavi"

    invoke-virtual {v1, p1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    .line 703
    invoke-virtual {v0, v1}, Lcom/chinatsp/dashboard/model/YandexNaviModel;->setNaviRunning(Z)V

    .line 704
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "updateYandexNaviState: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, " -> "

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "InteractiveModel"

    invoke-static {v0, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    :cond_1
    return-void

    return-void
.end method
