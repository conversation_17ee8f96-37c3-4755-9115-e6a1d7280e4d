.class public Lcom/chinatsp/dashboard/utils/MediaConstants;
.super Ljava/lang/Object;
.source "MediaConstants.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/chinatsp/dashboard/utils/MediaConstants$SettingStateValue;,
        Lcom/chinatsp/dashboard/utils/MediaConstants$ThemeValue;,
        Lcom/chinatsp/dashboard/utils/MediaConstants$RequestValue;,
        Lcom/chinatsp/dashboard/utils/MediaConstants$DriverType;,
        Lcom/chinatsp/dashboard/utils/MediaConstants$DisPlayType;,
        Lcom/chinatsp/dashboard/utils/MediaConstants$AnimationType;,
        Lcom/chinatsp/dashboard/utils/MediaConstants$SourceID;,
        Lcom/chinatsp/dashboard/utils/MediaConstants$MediaType;,
        Lcom/chinatsp/dashboard/utils/MediaConstants$PlayState;
    }
.end annotation


# static fields
.field public static final ACTION_SHARE_PRODUCT_TTS:Ljava/lang/String; = "chinatsp.action.virtual.tts"

.field public static final FUEL_SHARE_PATH:Ljava/lang/String; = "fuel_share_path"

.field public static final FUEL_SHARE_VALUE:Ljava/lang/String; = "fuel_share_value"

.field public static final KEY_SHARE_TTS:Ljava/lang/String; = "extra"

.field public static final MILEAGE_SHARE_PATH:Ljava/lang/String; = "mileage_share_path"

.field public static final MILEAGE_SHARE_VALUE:Ljava/lang/String; = "mileage_share_value"

.field public static final PACKAGE_NAVI:Ljava/lang/String; = "cn.loopon.app.navi"

.field public static final PACKAGE_SYSTEM_UI:Ljava/lang/String; = "com.android.systemui"

.field public static final SETTINGS_KEY_LIGHT_SHOW_INDEX:Ljava/lang/String; = "key_light_show_index"

.field public static final SETTING_KEY_METER_PAGE_STATE:Ljava/lang/String; = "key_meter_page_state"

.field public static final SETTING_VALUE_METER_PAGE_COMFORT:I = 0x0

.field public static final SETTING_VALUE_METER_PAGE_IACC:I = 0x4

.field public static final SETTING_VALUE_METER_PAGE_NAVI:I = 0x2

.field public static final SETTING_VALUE_METER_PAGE_OTHER:I = 0x5

.field public static final SETTING_VALUE_METER_PAGE_SPORT:I = 0x1

.field public static final SETTING_VALUE_METER_PAGE_TRACK:I = 0x3

.field public static final VALUE_SHARE_FUEL:Ljava/lang/String; = "EXTRA_SHARE_OIL"

.field public static final VALUE_SHARE_MILEAGE:Ljava/lang/String; = "EXTRA_SHARE_MILEAGE"

.field public static final VOICE_ACTION_SWITCH_NAVI:Ljava/lang/String; = "voice_action_switch_navi"

.field public static final VOICE_KEY_SWITCH:Ljava/lang/String; = "key_switch"

.field public static final VOICE_KEY_TYPE_DISPLAY:Ljava/lang/String; = "key_type_display"

.field public static final VOICE_VALUE_DISPLAY_METER:I = 0x1

.field public static final VOICE_VALUE_STATE_OFF:I = 0x2

.field public static final VOICE_VALUE_STATE_ON:I = 0x1

.field public static final WHEEL_ACTION_TOUCH:Ljava/lang/String; = "com.coagent.intent.action.KEY_CHANGED"

.field public static final WHEEL_KEY_CODE:Ljava/lang/String; = "Key_code"

.field public static final WHEEL_KEY_STATE:Ljava/lang/String; = "Key_state"

.field public static final WHEEL_VALUE_CODE_TURN_PAGE:Ljava/lang/String; = "TURN_PAGE"

.field public static final WHEEL_VALUE_STATE_DOWN:Ljava/lang/String; = "DOWN"

.field public static final WHEEL_VALUE_STATE_LONG_EVENT:Ljava/lang/String; = "LONG_EVENT"

.field public static final WHEEL_VALUE_STATE_LONG_UP:Ljava/lang/String; = "LONG_UP"

.field public static final WHEEL_VALUE_STATE_UP:Ljava/lang/String; = "UP"


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 12
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
