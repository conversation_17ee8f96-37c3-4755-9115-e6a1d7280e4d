.class public final Lcom/example/mxextend/ExtendApi;
.super Ljava/lang/Object;
.source "ExtendApi.kt"

# interfaces
.implements Lcom/example/mxextend/IExtendApi;
.implements Lcom/example/mxextend/ExtendConstants;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/example/mxextend/ExtendApi$Companion;
    }
.end annotation

.annotation runtime Lkotlin/Metadata;
    d1 = {
        "\u0000\u009c\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0010\u000e\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0008\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0010\u0002\n\u0002\u0008\u0014\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u000b\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0005\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000e\n\u0002\u0018\u0002\n\u0002\u0008\u0006\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0008\u0004\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u0007\n\u0002\u0018\u0002\n\u0002\u0008\r\n\u0002\u0018\u0002\n\u0002\u0008\u0003\n\u0002\u0018\u0002\n\u0002\u0008\u0002\n\u0002\u0018\u0002\n\u0002\u0008\u000b\u0018\u0000 \u0090\u00022\u00020\u00012\u00020\u0002:\u0002\u0090\u0002B\u0007\u0008\u0012\u00a2\u0006\u0002\u0010\u0003B\u0011\u0008\u0010\u0012\u0008\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010P\u001a\u00020.2\u0006\u0010Q\u001a\u00020.H\u0016J\u0012\u0010R\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010\tH\u0016J\u0012\u0010U\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010\u001aH\u0016J\u0012\u0010V\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010 H\u0016J\u0012\u0010W\u001a\u00020S2\u0008\u0010X\u001a\u0004\u0018\u00010$H\u0016J\u0012\u0010Y\u001a\u00020S2\u0008\u0010Z\u001a\u0004\u0018\u00010&H\u0016J\u0012\u0010[\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u000102H\u0016J\u0012\u0010\\\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u000109H\u0016J\u0012\u0010]\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010CH\u0016J\u0012\u0010^\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010EH\u0016J\u0012\u0010_\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010GH\u0016J\u0012\u0010`\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010*H\u0016J\u0012\u0010a\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010KH\u0016J\u0012\u0010b\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010OH\u0016J<\u0010c\u001a\u00020.2\u0008\u0010d\u001a\u0004\u0018\u00010\u00112\u0008\u0010e\u001a\u0004\u0018\u00010\u00112\u0006\u0010f\u001a\u00020.2\u0006\u0010g\u001a\u00020h2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J \u0010l\u001a\u00020S2\u0006\u0010m\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0008\u0010o\u001a\u00020SH\u0016J \u0010p\u001a\u00020S2\u0006\u0010q\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020r\u0018\u00010jH\u0016J\u0008\u0010s\u001a\u00020.H\u0016J\u0008\u0010t\u001a\u00020.H\u0016J\u001a\u0010u\u001a\u00020S2\u0010\u0010i\u001a\u000c\u0012\u0006\u0012\u0004\u0018\u00010n\u0018\u00010jH\u0016J$\u0010v\u001a\u00020S2\u0008\u0010w\u001a\u0004\u0018\u00010\u00112\u0010\u0010i\u001a\u000c\u0012\u0006\u0012\u0004\u0018\u00010x\u0018\u00010jH\u0016J$\u0010y\u001a\u00020S2\u0008\u0010w\u001a\u0004\u0018\u00010\u00112\u0010\u0010i\u001a\u000c\u0012\u0006\u0012\u0004\u0018\u00010x\u0018\u00010jH\u0016J\u001a\u0010z\u001a\u00020S2\u0010\u0010i\u001a\u000c\u0012\u0006\u0012\u0004\u0018\u00010x\u0018\u00010jH\u0016J$\u0010{\u001a\u00020S2\u0008\u0010w\u001a\u0004\u0018\u00010\u00112\u0010\u0010i\u001a\u000c\u0012\u0006\u0012\u0004\u0018\u00010x\u0018\u00010jH\u0016J\u0010\u0010|\u001a\u00020S2\u0006\u0010}\u001a\u00020.H\u0016J \u0010~\u001a\u00020.2\u0006\u0010\u007f\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0012\u0010\u0080\u0001\u001a\u00020.2\u0007\u0010\u0081\u0001\u001a\u00020.H\u0016J-\u0010\u0082\u0001\u001a\u00020.2\u0006\u0010m\u001a\u00020.2\n\u0010\u0083\u0001\u001a\u0005\u0018\u00010\u0084\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J#\u0010\u0085\u0001\u001a\u00020S2\u0007\u0010\u0086\u0001\u001a\u00020.2\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030\u0084\u0001\u0018\u00010jH\u0016J\u001a\u0010\u0087\u0001\u001a\u00020S2\u0007\u0010\u0088\u0001\u001a\u00020\u00112\u0006\u0010i\u001a\u00020\u000bH\u0016J\t\u0010\u0089\u0001\u001a\u00020.H\u0016J.\u0010\u008a\u0001\u001a\u00020S2\u0008\u0010\u008b\u0001\u001a\u00030\u008c\u00012\u0008\u0010\u008d\u0001\u001a\u00030\u008c\u00012\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030\u008e\u0001\u0018\u00010jH\u0016J\u001a\u0010\u008f\u0001\u001a\u00020S2\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030\u0090\u0001\u0018\u00010jH\u0016J\u001a\u0010\u0091\u0001\u001a\u00020S2\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030\u0084\u0001\u0018\u00010jH\u0016J\u0018\u0010\u0092\u0001\u001a\u00020S2\r\u0010i\u001a\t\u0012\u0005\u0012\u00030\u0084\u00010jH\u0016J\u001b\u0010\u0093\u0001\u001a\u00020S2\u0010\u0010i\u001a\u000c\u0012\u0006\u0012\u0004\u0018\u00010k\u0018\u00010jH\u0016J \u0010\u0094\u0001\u001a\u00020S2\u0006\u0010m\u001a\u00020.2\r\u0010i\u001a\t\u0012\u0005\u0012\u00030\u0084\u00010jH\u0016J\u001c\u0010\u0095\u0001\u001a\u00020S2\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u0096\u0001\u0018\u00010jH\u0016J\t\u0010\u0097\u0001\u001a\u00020.H\u0016J\t\u0010\u0098\u0001\u001a\u00020.H\u0016J\t\u0010\u0099\u0001\u001a\u00020.H\u0016J\t\u0010\u009a\u0001\u001a\u00020.H\u0016J\t\u0010\u009b\u0001\u001a\u00020.H\u0016J\t\u0010\u009c\u0001\u001a\u00020.H\u0016J\u001c\u0010\u009d\u0001\u001a\u00020S2\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u009e\u0001\u0018\u00010jH\u0016J\t\u0010\u009f\u0001\u001a\u00020.H\u0016J\u001b\u0010\u00a0\u0001\u001a\u00020S2\u0010\u0010i\u001a\u000c\u0012\u0006\u0012\u0004\u0018\u00010k\u0018\u00010jH\u0016J\t\u0010\u00a1\u0001\u001a\u00020.H\u0016J\t\u0010\u00a2\u0001\u001a\u00020.H\u0016J+\u0010\u00a3\u0001\u001a\u00020S2\u0007\u0010\u00a4\u0001\u001a\u00020.2\u0007\u0010\u00a5\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0019\u0010\u00a6\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\t\u0010\u00a7\u0001\u001a\u00020.H\u0016J$\u0010\u00a8\u0001\u001a\u00020S2\t\u0010\u00a9\u0001\u001a\u0004\u0018\u00010\u00112\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0011\u0010\u00aa\u0001\u001a\u00020S2\u0006\u0010}\u001a\u00020.H\u0002J\'\u0010\u00ab\u0001\u001a\u00020S2\t\u0010\u00ac\u0001\u001a\u0004\u0018\u00010\u00112\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u00ad\u0001\u0018\u00010jH\u0016J\t\u0010\u00ae\u0001\u001a\u00020.H\u0016J\t\u0010\u00af\u0001\u001a\u00020.H\u0016J#\u0010\u00b0\u0001\u001a\u00020.2\u0008\u0010e\u001a\u0004\u0018\u00010\u00112\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J+\u0010\u00b0\u0001\u001a\u00020.2\u0008\u0010e\u001a\u0004\u0018\u00010\u00112\u0006\u0010f\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J!\u0010\u00b1\u0001\u001a\u00020S2\u0006\u0010m\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\t\u0010\u00b2\u0001\u001a\u00020.H\u0016J,\u0010\u00b3\u0001\u001a\u00020.2\u0006\u0010Q\u001a\u00020.2\u0007\u0010\u00b4\u0001\u001a\u00020.2\u0010\u0010i\u001a\u000c\u0012\u0006\u0012\u0004\u0018\u00010n\u0018\u00010jH\u0016J*\u0010\u00b5\u0001\u001a\u00020.2\u0006\u0010Q\u001a\u00020.2\u0007\u0010\u00b4\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J.\u0010\u00b6\u0001\u001a\u00020S2\u0007\u0010\u00b7\u0001\u001a\u00020\"2\n\u0010\u0083\u0001\u001a\u0005\u0018\u00010\u0084\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J-\u0010\u00b6\u0001\u001a\u00020S2\u0007\u0010\u00b8\u0001\u001a\u00020\"2\t\u0010\u00b9\u0001\u001a\u0004\u0018\u00010\u00112\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J.\u0010\u00ba\u0001\u001a\u00020S2\n\u0010\u0083\u0001\u001a\u0005\u0018\u00010\u0084\u00012\u0007\u0010\u00bb\u0001\u001a\u00020\"2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J&\u0010\u00bc\u0001\u001a\u00020S2\n\u0010\u0083\u0001\u001a\u0005\u0018\u00010\u00bd\u00012\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030\u00be\u0001\u0018\u00010jH\u0016J%\u0010\u00bf\u0001\u001a\u00020.2\n\u0010\u00c0\u0001\u001a\u0005\u0018\u00010\u00c1\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J+\u0010\u00bf\u0001\u001a\u00020.2\u0007\u0010\u00c2\u0001\u001a\u00020.2\u0007\u0010\u00c3\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0013\u0010\u00c4\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010\tH\u0016J\u0013\u0010\u00c5\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010\u001aH\u0016J\u0013\u0010\u00c6\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010 H\u0016J\u0013\u0010\u00c7\u0001\u001a\u00020S2\u0008\u0010X\u001a\u0004\u0018\u00010$H\u0016J\u0013\u0010\u00c8\u0001\u001a\u00020S2\u0008\u0010Z\u001a\u0004\u0018\u00010&H\u0016J\u0013\u0010\u00c9\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u000102H\u0016J\u0013\u0010\u00ca\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u000109H\u0016J\u0013\u0010\u00cb\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010CH\u0016J\u0013\u0010\u00cc\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010EH\u0016J\u0013\u0010\u00cd\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010GH\u0016J\u0015\u0010\u00ce\u0001\u001a\u00020S2\n\u0010\u00cf\u0001\u001a\u0005\u0018\u00010\u00d0\u0001H\u0002J\u0013\u0010\u00d1\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010*H\u0016J\t\u0010\u00d2\u0001\u001a\u00020SH\u0002J\u0013\u0010\u00d3\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010KH\u0016J\u0013\u0010\u00d4\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010OH\u0016J%\u0010\u00d5\u0001\u001a\u00020.2\n\u0010\u00d6\u0001\u001a\u0005\u0018\u00010\u00d7\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J%\u0010\u00d8\u0001\u001a\u00020S2\u0008\u0010e\u001a\u0004\u0018\u00010\u00112\u0010\u0010i\u001a\u000c\u0012\u0006\u0012\u0004\u0018\u00010k\u0018\u00010jH\u0016J(\u0010\u00d9\u0001\u001a\u00020.2\n\u0010\u00da\u0001\u001a\u0005\u0018\u00010\u00db\u00012\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u00dc\u0001\u0018\u00010jH\u0016J#\u0010\u00dd\u0001\u001a\u00020S2\u0008\u0010e\u001a\u0004\u0018\u00010\u00112\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J%\u0010\u00de\u0001\u001a\u00020.2\n\u0010\u00d6\u0001\u001a\u0005\u0018\u00010\u00df\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\"\u0010\u00e0\u0001\u001a\u00020S2\u0006\u0010m\u001a\u00020.2\u000f\u0010i\u001a\u000b\u0012\u0005\u0012\u00030\u00e1\u0001\u0018\u00010jH\u0016J%\u0010\u00e2\u0001\u001a\u00020S2\u0008\u0010e\u001a\u0004\u0018\u00010\u00112\u0010\u0010i\u001a\u000c\u0012\u0006\u0012\u0004\u0018\u00010k\u0018\u00010jH\u0016J-\u0010\u00e3\u0001\u001a\u00020.2\u0008\u0010d\u001a\u0004\u0018\u00010\u00112\u0008\u0010e\u001a\u0004\u0018\u00010\u00112\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J%\u0010\u00e4\u0001\u001a\u00020S2\n\u0010\u00e5\u0001\u001a\u0005\u0018\u00010\u00e6\u00012\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020k\u0018\u00010jH\u0016J-\u0010\u00e7\u0001\u001a\u00020S2\u0006\u0010m\u001a\u00020.2\u0007\u0010\u00e8\u0001\u001a\u00020\u00112\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u00e9\u0001\u0018\u00010jH\u0002J \u0010\u00ea\u0001\u001a\u00020.2\u0007\u0010\u00eb\u0001\u001a\u00020.2\u000c\u0010i\u001a\u0008\u0012\u0004\u0012\u00020n0jH\u0016J\u001d\u0010\u00ec\u0001\u001a\u00020.2\u0008\u0010\u00ed\u0001\u001a\u00030\u008c\u00012\u0008\u0010\u00ee\u0001\u001a\u00030\u008c\u0001H\u0016J!\u0010\u00ef\u0001\u001a\u00020S2\u0007\u0010\u00f0\u0001\u001a\u00020\"2\r\u0010i\u001a\t\u0012\u0005\u0012\u00030\u00f1\u00010jH\u0016J\"\u0010\u00f2\u0001\u001a\u00020S2\u0007\u0010\u00f3\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J!\u0010\u00f4\u0001\u001a\u00020S2\u0007\u0010\u00f0\u0001\u001a\u00020\"2\r\u0010i\u001a\t\u0012\u0005\u0012\u00030\u00f1\u00010jH\u0016J\u0019\u0010\u00f5\u0001\u001a\u00020S2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0012\u0010\u00f6\u0001\u001a\u00020.2\u0007\u0010\u00f7\u0001\u001a\u00020.H\u0016J!\u0010\u00f8\u0001\u001a\u00020.2\u0006\u0010m\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\t\u0010\u00f9\u0001\u001a\u00020SH\u0002J\u0013\u0010\u00fa\u0001\u001a\u00020S2\u0008\u0010T\u001a\u0004\u0018\u00010MH\u0016J\"\u0010\u00fb\u0001\u001a\u00020.2\u0007\u0010\u00fc\u0001\u001a\u00020\"2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J(\u0010\u00fd\u0001\u001a\u00020.2\n\u0010\u00fe\u0001\u001a\u0005\u0018\u00010\u00ff\u00012\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u00e9\u0001\u0018\u00010jH\u0016J\u001e\u0010\u0080\u0002\u001a\u00020S2\u0007\u0010\u0081\u0002\u001a\u00020.2\n\u0010\u0082\u0002\u001a\u0005\u0018\u00010\u0083\u0002H\u0016J-\u0010\u0084\u0002\u001a\u00020.2\u0006\u0010m\u001a\u00020.2\u0007\u0010\u00b4\u0001\u001a\u00020.2\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u0084\u0001\u0018\u00010jH\u0016J(\u0010\u0085\u0002\u001a\u00020.2\n\u0010\u00fe\u0001\u001a\u0005\u0018\u00010\u0086\u00022\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u00e9\u0001\u0018\u00010jH\u0016J\"\u0010\u0087\u0002\u001a\u00020.2\u0007\u0010\u00a4\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J+\u0010\u0087\u0002\u001a\u00020.2\u0007\u0010\u00a4\u0001\u001a\u00020.2\u0007\u0010\u00a5\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u001c\u0010\u0088\u0002\u001a\u00020S2\u0011\u0010i\u001a\r\u0012\u0007\u0012\u0005\u0018\u00010\u0096\u0001\u0018\u00010jH\u0016J\u0012\u0010\u0089\u0002\u001a\u00020S2\u0007\u0010\u0081\u0002\u001a\u00020.H\u0016J\"\u0010\u008a\u0002\u001a\u00020S2\u0007\u0010\u00b4\u0001\u001a\u00020.2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\u0019\u0010\u008b\u0002\u001a\u00020S2\u000e\u0010i\u001a\n\u0012\u0004\u0012\u00020n\u0018\u00010jH\u0016J\t\u0010\u008c\u0002\u001a\u00020SH\u0016J\t\u0010\u008d\u0002\u001a\u00020SH\u0016J\u0012\u0010\u008e\u0002\u001a\u00020S2\u0007\u0010\u008f\u0002\u001a\u00020,H\u0002R\u0014\u0010\u0007\u001a\u0008\u0012\u0004\u0012\u00020\t0\u0008X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u000c\u0010\r\"\u0004\u0008\u000e\u0010\u000fR\u001c\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008\u0012\u0010\u0013\"\u0004\u0008\u0014\u0010\u0015R\u0010\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0018\u001a\u0008\u0012\u0004\u0012\u00020\u001a0\u0019X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001f\u001a\u0008\u0012\u0004\u0012\u00020 0\u0008X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\"X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010#\u001a\u0008\u0012\u0004\u0012\u00020$0\u0008X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010%\u001a\u0008\u0012\u0004\u0012\u00020&0\u0008X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010)\u001a\u0008\u0012\u0004\u0012\u00020*0\u0019X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020,X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020.X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u000200X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u00101\u001a\u0008\u0012\u0004\u0012\u0002020\u0008X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u00103\u001a\u00020.X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u00084\u00105\"\u0004\u00086\u00107R\u0014\u00108\u001a\u0008\u0012\u0004\u0012\u0002090\u0008X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010:\u001a\u00020.X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010;\u001a\u0004\u0018\u00010<X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010=\u001a\u00020\"X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\u0008>\u0010?\"\u0004\u0008@\u0010AR\u0014\u0010B\u001a\u0008\u0012\u0004\u0012\u00020C0\u0008X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010D\u001a\u0008\u0012\u0004\u0012\u00020E0\u0008X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010F\u001a\u0008\u0012\u0004\u0012\u00020G0\u0008X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010H\u001a\u00020IX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010J\u001a\u0008\u0012\u0004\u0012\u00020K0\u0008X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010L\u001a\u0004\u0018\u00010MX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010N\u001a\u0008\u0012\u0004\u0012\u00020O0\u0008X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0091\u0002"
    }
    d2 = {
        "Lcom/example/mxextend/ExtendApi;",
        "Lcom/example/mxextend/IExtendApi;",
        "Lcom/example/mxextend/ExtendConstants;",
        "()V",
        "context",
        "Landroid/content/Context;",
        "(Landroid/content/Context;)V",
        "accountListeners",
        "Ljava/util/LinkedList;",
        "Lcom/example/mxextend/listener/IAccountListener;",
        "cacheExtendCallBack",
        "Lcom/example/mxextend/listener/IExtendJsonCallback;",
        "getCacheExtendCallBack",
        "()Lcom/example/mxextend/listener/IExtendJsonCallback;",
        "setCacheExtendCallBack",
        "(Lcom/example/mxextend/listener/IExtendJsonCallback;)V",
        "cacheProtocol",
        "",
        "getCacheProtocol",
        "()Ljava/lang/String;",
        "setCacheProtocol",
        "(Ljava/lang/String;)V",
        "displaySurfaceView",
        "Lcom/example/mxextend/widget/DisplaySurfaceView;",
        "extendListeners",
        "Ljava/util/concurrent/CopyOnWriteArrayList;",
        "Lcom/example/mxextend/listener/IExtendListener;",
        "extendServiceInterface",
        "Lcom/mxnavi/busines/ExtendServiceInterface;",
        "gson",
        "Lcom/google/gson/Gson;",
        "homeOrCompanyListeners",
        "Lcom/example/mxextend/listener/IHomeOrCompanyListener;",
        "isBind",
        "",
        "locationListeners",
        "Lcom/example/mxextend/listener/ILocationChangedListener;",
        "locationUpdateCallBacks",
        "Lcom/example/mxextend/listener/ILocationUpdateCallBack;",
        "mDeathRecipient",
        "Landroid/os/IBinder$DeathRecipient;",
        "mServiceConnectedListeners",
        "Lcom/example/mxextend/listener/IServiceConnectedListener;",
        "mServiceConnection",
        "Landroid/content/ServiceConnection;",
        "mShowMode",
        "",
        "mainHandler",
        "Landroid/os/Handler;",
        "manualVoiceInteractionListeners",
        "Lcom/example/mxextend/listener/IManualVoiceInteractionListener;",
        "naviStateResultcode",
        "getNaviStateResultcode",
        "()I",
        "setNaviStateResultcode",
        "(I)V",
        "pageChangedListeners",
        "Lcom/example/mxextend/listener/IPageChangedListener;",
        "pageStatus",
        "receiver",
        "Lcom/example/mxextend/receiver/MxNaviStatusReceiver;",
        "reconnect",
        "getReconnect",
        "()Z",
        "setReconnect",
        "(Z)V",
        "routeDataListeners",
        "Lcom/example/mxextend/listener/IRouteDataListener;",
        "searchDataListeners",
        "Lcom/example/mxextend/listener/ISearchDataListener;",
        "searchResultListeners",
        "Lcom/example/mxextend/listener/ISearchResultListener;",
        "statusChangedListener",
        "Lcom/mxnavi/busines/IStatusChangedListener$Stub;",
        "suggestionResultListeners",
        "Lcom/example/mxextend/listener/ISuggestionResultListener;",
        "voiceClickListener",
        "Lcom/example/mxextend/listener/IVoiceBtnCallBack;",
        "weatherInfoListeners",
        "Lcom/example/mxextend/listener/IWeatherInfoListener;",
        "accountOpera",
        "actionType",
        "addAccountChangedListener",
        "",
        "listener",
        "addExtendListener",
        "addHomeOrCompanyChangedListener",
        "addLocationChangedListener",
        "locationChangedListener",
        "addLocationUpdateCallBack",
        "callBack",
        "addManualVoiceInteractionListener",
        "addPageChangedListener",
        "addRouteDataListener",
        "addSearchDataListener",
        "addSearchResultListener",
        "addServiceConnectedListener",
        "addSuggestDataListener",
        "addWeatherInfoListenerListener",
        "aroundSearch",
        "nearbyKey",
        "searchKey",
        "searchAction",
        "distance",
        "",
        "callback",
        "Lcom/example/mxextend/listener/IExtendCallback;",
        "Lcom/example/mxextend/entity/SearchResultModel;",
        "backToMap",
        "type",
        "Lcom/example/mxextend/entity/ExtendBaseModel;",
        "bindMxExtService",
        "calculateRoad",
        "i",
        "Lcom/example/mxextend/entity/RouteResult;",
        "cancelNavi",
        "cancelNaviBack",
        "cancelNavigation",
        "carltdBindRequest",
        "userCenterId",
        "Lcom/example/mxextend/entity/CarltdRequestInfoModel;",
        "carltdCheckBindRequest",
        "carltdLoginRequest",
        "carltdUnBindRequest",
        "changeInstrumentShowMode",
        "showMode",
        "changeNaviRoutePrefer",
        "strategy",
        "changePreference",
        "preferenceId",
        "collectByPoi",
        "info",
        "Lcom/example/mxextend/entity/LocationInfo;",
        "deleteViaPoint",
        "index",
        "doRequest",
        "protocol",
        "getAccountStatus",
        "getAddressByCoordinate",
        "lon",
        "",
        "lat",
        "Lcom/example/mxextend/entity/AddressModel;",
        "getCityInfo",
        "Lcom/example/mxextend/entity/CityInfo;",
        "getDestInfo",
        "getEndPoint",
        "getFavoriteList",
        "getHomeOrCompanyData",
        "getMXnaviAppLoginResult",
        "Lcom/example/mxextend/entity/CarltdLoginResultBean;",
        "getNaviStage",
        "getNaviState",
        "getNaviType",
        "getPassPointNum",
        "getRemainDistance",
        "getRemainTime",
        "getRouteSummaryList",
        "Lcom/example/mxextend/entity/RouteSummaryModel;",
        "getScaleLevel",
        "getSearchHistoryData",
        "getSpeakMode",
        "goFavorite",
        "goHomeOrCompany",
        "destType",
        "directNavi",
        "goSetting",
        "goTeamTrip",
        "goToNearbyGasStation",
        "nearbyName",
        "initDisplaySurfaceView",
        "isExistInCollect",
        "name",
        "Lcom/example/mxextend/entity/CollectInfoModel;",
        "isInNavi",
        "isVolumeMute",
        "keywordSearch",
        "lightFigureSwitch",
        "lookOverView",
        "mapOpera",
        "operaType",
        "naviOpera",
        "naviToPoi",
        "isRefres",
        "isRefresh",
        "poiName",
        "navigation",
        "isShowMutilPage",
        "navigationPlanningRoute",
        "Lcom/example/mxextend/entity/LocationDetailModel;",
        "Lcom/example/mxextend/entity/LocationDetail;",
        "pageOprea",
        "data",
        "Lcom/mxnavi/busines/entity/PageOpreaData;",
        "pageId",
        "action",
        "removeAccountChangedListener",
        "removeExtendListener",
        "removeHomeOrCompanyChangedListener",
        "removeLocationChangedListener",
        "removeLocationUpdateCallBack",
        "removeManualVoiceInteractionListener",
        "removePageChangedListener",
        "removeRouteDataListener",
        "removeSearchDataListener",
        "removeSearchResultListener",
        "removeSelfFromParent",
        "child",
        "Landroid/view/View;",
        "removeServiceConnectedListener",
        "removeStatusChangedListener",
        "removeSuggestDataListener",
        "removeWeatherInfoListenerListener",
        "requestAddPass",
        "model",
        "Lcom/mxnavi/busines/entity/ModifyNaviViaModel;",
        "requestAlongRouteData",
        "requestGuideInfo",
        "requestGuideInfoModel",
        "Lcom/mxnavi/busines/entity/RequestGuideInfoModel;",
        "Lcom/example/mxextend/entity/GuideInfoModel;",
        "requestPoiData",
        "requestRouteEx",
        "Lcom/mxnavi/busines/entity/RequestRouteExModel;",
        "restoreNavigation",
        "Lcom/example/mxextend/entity/HistoricaRouteInfo;",
        "searchAlongRoute",
        "searchNearByPoi",
        "searchPoi",
        "requestSearchData",
        "Lcom/mxnavi/busines/entity/RequestSearchData;",
        "searchTraffic",
        "keyWord",
        "Lcom/example/mxextend/entity/TrafficInfoModel;",
        "selectRoute",
        "selectId",
        "sendWxPosition",
        "longitude",
        "latitude",
        "setCompanyAddressResult",
        "needJumpToNavi",
        "Lcom/example/mxextend/entity/HomeCompanyItemBean;",
        "setDayNightStyle",
        "style",
        "setHomeAddressResult",
        "setNaviScreen",
        "setSpeakMode",
        "speakMode",
        "setSpecialPoi",
        "setStatusChangedListener",
        "setVoiceBtnCallBack",
        "setVolumeMute",
        "isMute",
        "showFrontTraffic",
        "showTrafficModel",
        "Lcom/mxnavi/busines/entity/ShowFrontTrafficModel;",
        "showInstrumentProjection",
        "screenType",
        "view",
        "Landroid/view/ViewGroup;",
        "showMyLocation",
        "showTraffic",
        "Lcom/mxnavi/busines/entity/ShowTrafficModel;",
        "specialPoiNavi",
        "startMXnaviAppResult",
        "stopInstrumentProjection",
        "switchParallelRoad",
        "switchRoute",
        "transStsRequest",
        "unBindMxExtService",
        "unbindService",
        "conn",
        "Companion",
        "NaviClient_release"
    }
    k = 0x1
    mv = {
        0x1,
        0x7,
        0x1
    }
    xi = 0x30
.end annotation


# static fields
.field private static final ACTION_BIND_EXTERNAL_SERVICE:Ljava/lang/String; = "com.mxnavi.remote_service"

.field public static final Companion:Lcom/example/mxextend/ExtendApi$Companion;

.field private static final SERVER_PACKAGE_NAME:Ljava/lang/String; = "cn.loopon.app.navi"

.field private static final TAG:Ljava/lang/String;

.field public static final VERSION:Ljava/lang/String; = "1.0.31"


# instance fields
.field private final accountListeners:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lcom/example/mxextend/listener/IAccountListener;",
            ">;"
        }
    .end annotation
.end field

.field private cacheExtendCallBack:Lcom/example/mxextend/listener/IExtendJsonCallback;

.field private cacheProtocol:Ljava/lang/String;

.field private context:Landroid/content/Context;

.field private displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

.field private final extendListeners:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lcom/example/mxextend/listener/IExtendListener;",
            ">;"
        }
    .end annotation
.end field

.field private extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

.field private gson:Lcom/google/gson/Gson;

.field private final homeOrCompanyListeners:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lcom/example/mxextend/listener/IHomeOrCompanyListener;",
            ">;"
        }
    .end annotation
.end field

.field private isBind:Z

.field private final locationListeners:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lcom/example/mxextend/listener/ILocationChangedListener;",
            ">;"
        }
    .end annotation
.end field

.field private final locationUpdateCallBacks:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lcom/example/mxextend/listener/ILocationUpdateCallBack;",
            ">;"
        }
    .end annotation
.end field

.field private final mDeathRecipient:Landroid/os/IBinder$DeathRecipient;

.field private final mServiceConnectedListeners:Ljava/util/concurrent/CopyOnWriteArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/concurrent/CopyOnWriteArrayList<",
            "Lcom/example/mxextend/listener/IServiceConnectedListener;",
            ">;"
        }
    .end annotation
.end field

.field private final mServiceConnection:Landroid/content/ServiceConnection;

.field private mShowMode:I

.field private final mainHandler:Landroid/os/Handler;

.field private final manualVoiceInteractionListeners:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lcom/example/mxextend/listener/IManualVoiceInteractionListener;",
            ">;"
        }
    .end annotation
.end field

.field private naviStateResultcode:I

.field private final pageChangedListeners:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lcom/example/mxextend/listener/IPageChangedListener;",
            ">;"
        }
    .end annotation
.end field

.field private pageStatus:I

.field private receiver:Lcom/example/mxextend/receiver/MxNaviStatusReceiver;

.field private reconnect:Z

.field private final routeDataListeners:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lcom/example/mxextend/listener/IRouteDataListener;",
            ">;"
        }
    .end annotation
.end field

.field private final searchDataListeners:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lcom/example/mxextend/listener/ISearchDataListener;",
            ">;"
        }
    .end annotation
.end field

.field private final searchResultListeners:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lcom/example/mxextend/listener/ISearchResultListener;",
            ">;"
        }
    .end annotation
.end field

.field private final statusChangedListener:Lcom/mxnavi/busines/IStatusChangedListener$Stub;

.field private final suggestionResultListeners:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lcom/example/mxextend/listener/ISuggestionResultListener;",
            ">;"
        }
    .end annotation
.end field

.field private voiceClickListener:Lcom/example/mxextend/listener/IVoiceBtnCallBack;

.field private final weatherInfoListeners:Ljava/util/LinkedList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/LinkedList<",
            "Lcom/example/mxextend/listener/IWeatherInfoListener;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lcom/example/mxextend/ExtendApi$Companion;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lcom/example/mxextend/ExtendApi$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V

    sput-object v0, Lcom/example/mxextend/ExtendApi;->Companion:Lcom/example/mxextend/ExtendApi$Companion;

    .line 3234
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-class v1, Lcom/example/mxextend/ExtendApi;

    invoke-virtual {v1}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", ver.  1.0.31"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    sput-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    return-void
.end method

.method private constructor <init>()V
    .locals 2

    .line 127
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 85
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnectedListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 86
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 88
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->accountListeners:Ljava/util/LinkedList;

    .line 90
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->locationListeners:Ljava/util/LinkedList;

    .line 92
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->routeDataListeners:Ljava/util/LinkedList;

    .line 94
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->searchDataListeners:Ljava/util/LinkedList;

    .line 96
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->searchResultListeners:Ljava/util/LinkedList;

    .line 98
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->suggestionResultListeners:Ljava/util/LinkedList;

    .line 100
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->locationUpdateCallBacks:Ljava/util/LinkedList;

    .line 102
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->pageChangedListeners:Ljava/util/LinkedList;

    .line 104
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->homeOrCompanyListeners:Ljava/util/LinkedList;

    .line 106
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->weatherInfoListeners:Ljava/util/LinkedList;

    .line 108
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->manualVoiceInteractionListeners:Ljava/util/LinkedList;

    .line 112
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    .line 114
    new-instance v0, Lcom/google/gson/Gson;

    invoke-direct {v0}, Lcom/google/gson/Gson;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    const/4 v0, 0x1

    .line 120
    iput v0, p0, Lcom/example/mxextend/ExtendApi;->mShowMode:I

    const/4 v0, -0x1

    .line 125
    iput v0, p0, Lcom/example/mxextend/ExtendApi;->pageStatus:I

    .line 129
    new-instance v0, Lcom/example/mxextend/ExtendApi$mServiceConnection$1;

    invoke-direct {v0, p0}, Lcom/example/mxextend/ExtendApi$mServiceConnection$1;-><init>(Lcom/example/mxextend/ExtendApi;)V

    check-cast v0, Landroid/content/ServiceConnection;

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnection:Landroid/content/ServiceConnection;

    .line 184
    new-instance v0, Lcom/example/mxextend/ExtendApi$mDeathRecipient$1;

    invoke-direct {v0, p0}, Lcom/example/mxextend/ExtendApi$mDeathRecipient$1;-><init>(Lcom/example/mxextend/ExtendApi;)V

    check-cast v0, Landroid/os/IBinder$DeathRecipient;

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->mDeathRecipient:Landroid/os/IBinder$DeathRecipient;

    .line 298
    new-instance v0, Lcom/example/mxextend/ExtendApi$statusChangedListener$1;

    invoke-direct {v0, p0}, Lcom/example/mxextend/ExtendApi$statusChangedListener$1;-><init>(Lcom/example/mxextend/ExtendApi;)V

    check-cast v0, Lcom/mxnavi/busines/IStatusChangedListener$Stub;

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->statusChangedListener:Lcom/mxnavi/busines/IStatusChangedListener$Stub;

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 2

    .line 221
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 85
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnectedListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 86
    new-instance v0, Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-direct {v0}, Ljava/util/concurrent/CopyOnWriteArrayList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    .line 88
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->accountListeners:Ljava/util/LinkedList;

    .line 90
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->locationListeners:Ljava/util/LinkedList;

    .line 92
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->routeDataListeners:Ljava/util/LinkedList;

    .line 94
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->searchDataListeners:Ljava/util/LinkedList;

    .line 96
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->searchResultListeners:Ljava/util/LinkedList;

    .line 98
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->suggestionResultListeners:Ljava/util/LinkedList;

    .line 100
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->locationUpdateCallBacks:Ljava/util/LinkedList;

    .line 102
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->pageChangedListeners:Ljava/util/LinkedList;

    .line 104
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->homeOrCompanyListeners:Ljava/util/LinkedList;

    .line 106
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->weatherInfoListeners:Ljava/util/LinkedList;

    .line 108
    new-instance v0, Ljava/util/LinkedList;

    invoke-direct {v0}, Ljava/util/LinkedList;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->manualVoiceInteractionListeners:Ljava/util/LinkedList;

    .line 112
    new-instance v0, Landroid/os/Handler;

    invoke-static {}, Landroid/os/Looper;->getMainLooper()Landroid/os/Looper;

    move-result-object v1

    invoke-direct {v0, v1}, Landroid/os/Handler;-><init>(Landroid/os/Looper;)V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    .line 114
    new-instance v0, Lcom/google/gson/Gson;

    invoke-direct {v0}, Lcom/google/gson/Gson;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    const/4 v0, 0x1

    .line 120
    iput v0, p0, Lcom/example/mxextend/ExtendApi;->mShowMode:I

    const/4 v0, -0x1

    .line 125
    iput v0, p0, Lcom/example/mxextend/ExtendApi;->pageStatus:I

    .line 129
    new-instance v0, Lcom/example/mxextend/ExtendApi$mServiceConnection$1;

    invoke-direct {v0, p0}, Lcom/example/mxextend/ExtendApi$mServiceConnection$1;-><init>(Lcom/example/mxextend/ExtendApi;)V

    check-cast v0, Landroid/content/ServiceConnection;

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnection:Landroid/content/ServiceConnection;

    .line 184
    new-instance v0, Lcom/example/mxextend/ExtendApi$mDeathRecipient$1;

    invoke-direct {v0, p0}, Lcom/example/mxextend/ExtendApi$mDeathRecipient$1;-><init>(Lcom/example/mxextend/ExtendApi;)V

    check-cast v0, Landroid/os/IBinder$DeathRecipient;

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->mDeathRecipient:Landroid/os/IBinder$DeathRecipient;

    .line 298
    new-instance v0, Lcom/example/mxextend/ExtendApi$statusChangedListener$1;

    invoke-direct {v0, p0}, Lcom/example/mxextend/ExtendApi$statusChangedListener$1;-><init>(Lcom/example/mxextend/ExtendApi;)V

    check-cast v0, Lcom/mxnavi/busines/IStatusChangedListener$Stub;

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->statusChangedListener:Lcom/mxnavi/busines/IStatusChangedListener$Stub;

    .line 222
    iput-object p1, p0, Lcom/example/mxextend/ExtendApi;->context:Landroid/content/Context;

    if-eqz p1, :cond_0

    .line 223
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->receiver:Lcom/example/mxextend/receiver/MxNaviStatusReceiver;

    if-nez v0, :cond_0

    .line 224
    new-instance v0, Lcom/example/mxextend/receiver/MxNaviStatusReceiver;

    invoke-direct {v0}, Lcom/example/mxextend/receiver/MxNaviStatusReceiver;-><init>()V

    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->receiver:Lcom/example/mxextend/receiver/MxNaviStatusReceiver;

    .line 225
    new-instance v0, Landroid/content/IntentFilter;

    invoke-direct {v0}, Landroid/content/IntentFilter;-><init>()V

    const-string v1, "com.mxnavi.extend.status.open"

    .line 226
    invoke-virtual {v0, v1}, Landroid/content/IntentFilter;->addAction(Ljava/lang/String;)V

    .line 227
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->receiver:Lcom/example/mxextend/receiver/MxNaviStatusReceiver;

    check-cast v1, Landroid/content/BroadcastReceiver;

    invoke-virtual {p1, v1, v0}, Landroid/content/Context;->registerReceiver(Landroid/content/BroadcastReceiver;Landroid/content/IntentFilter;)Landroid/content/Intent;

    .line 229
    :cond_0
    invoke-virtual {p0}, Lcom/example/mxextend/ExtendApi;->bindMxExtService()V

    return-void
.end method

.method public static final synthetic access$getAccountListeners$p(Lcom/example/mxextend/ExtendApi;)Ljava/util/LinkedList;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->accountListeners:Ljava/util/LinkedList;

    return-object p0
.end method

.method public static final synthetic access$getDisplaySurfaceView$p(Lcom/example/mxextend/ExtendApi;)Lcom/example/mxextend/widget/DisplaySurfaceView;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

    return-object p0
.end method

.method public static final synthetic access$getExtendListeners$p(Lcom/example/mxextend/ExtendApi;)Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->extendListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object p0
.end method

.method public static final synthetic access$getExtendServiceInterface$p(Lcom/example/mxextend/ExtendApi;)Lcom/mxnavi/busines/ExtendServiceInterface;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    return-object p0
.end method

.method public static final synthetic access$getGson$p(Lcom/example/mxextend/ExtendApi;)Lcom/google/gson/Gson;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    return-object p0
.end method

.method public static final synthetic access$getHomeOrCompanyListeners$p(Lcom/example/mxextend/ExtendApi;)Ljava/util/LinkedList;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->homeOrCompanyListeners:Ljava/util/LinkedList;

    return-object p0
.end method

.method public static final synthetic access$getMDeathRecipient$p(Lcom/example/mxextend/ExtendApi;)Landroid/os/IBinder$DeathRecipient;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->mDeathRecipient:Landroid/os/IBinder$DeathRecipient;

    return-object p0
.end method

.method public static final synthetic access$getMServiceConnectedListeners$p(Lcom/example/mxextend/ExtendApi;)Ljava/util/concurrent/CopyOnWriteArrayList;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnectedListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    return-object p0
.end method

.method public static final synthetic access$getMServiceConnection$p(Lcom/example/mxextend/ExtendApi;)Landroid/content/ServiceConnection;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnection:Landroid/content/ServiceConnection;

    return-object p0
.end method

.method public static final synthetic access$getMainHandler$p(Lcom/example/mxextend/ExtendApi;)Landroid/os/Handler;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    return-object p0
.end method

.method public static final synthetic access$getManualVoiceInteractionListeners$p(Lcom/example/mxextend/ExtendApi;)Ljava/util/LinkedList;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->manualVoiceInteractionListeners:Ljava/util/LinkedList;

    return-object p0
.end method

.method public static final synthetic access$getPageChangedListeners$p(Lcom/example/mxextend/ExtendApi;)Ljava/util/LinkedList;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->pageChangedListeners:Ljava/util/LinkedList;

    return-object p0
.end method

.method public static final synthetic access$getPageStatus$p(Lcom/example/mxextend/ExtendApi;)I
    .locals 0

    .line 81
    iget p0, p0, Lcom/example/mxextend/ExtendApi;->pageStatus:I

    return p0
.end method

.method public static final synthetic access$getTAG$cp()Ljava/lang/String;
    .locals 1

    .line 81
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    return-object v0
.end method

.method public static final synthetic access$getWeatherInfoListeners$p(Lcom/example/mxextend/ExtendApi;)Ljava/util/LinkedList;
    .locals 0

    .line 81
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->weatherInfoListeners:Ljava/util/LinkedList;

    return-object p0
.end method

.method public static final synthetic access$isBind$p(Lcom/example/mxextend/ExtendApi;)Z
    .locals 0

    .line 81
    iget-boolean p0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    return p0
.end method

.method public static final synthetic access$removeStatusChangedListener(Lcom/example/mxextend/ExtendApi;)V
    .locals 0

    .line 81
    invoke-direct {p0}, Lcom/example/mxextend/ExtendApi;->removeStatusChangedListener()V

    return-void
.end method

.method public static final synthetic access$setBind$p(Lcom/example/mxextend/ExtendApi;Z)V
    .locals 0

    .line 81
    iput-boolean p1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    return-void
.end method

.method public static final synthetic access$setExtendServiceInterface$p(Lcom/example/mxextend/ExtendApi;Lcom/mxnavi/busines/ExtendServiceInterface;)V
    .locals 0

    .line 81
    iput-object p1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    return-void
.end method

.method public static final synthetic access$setPageStatus$p(Lcom/example/mxextend/ExtendApi;I)V
    .locals 0

    .line 81
    iput p1, p0, Lcom/example/mxextend/ExtendApi;->pageStatus:I

    return-void
.end method

.method public static final synthetic access$setStatusChangedListener(Lcom/example/mxextend/ExtendApi;)V
    .locals 0

    .line 81
    invoke-direct {p0}, Lcom/example/mxextend/ExtendApi;->setStatusChangedListener()V

    return-void
.end method

.method public static final synthetic access$unbindService(Lcom/example/mxextend/ExtendApi;Landroid/content/ServiceConnection;)V
    .locals 0

    .line 81
    invoke-direct {p0, p1}, Lcom/example/mxextend/ExtendApi;->unbindService(Landroid/content/ServiceConnection;)V

    return-void
.end method

.method private static final addPageChangedListener$lambda$40(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IPageChangedListener;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 2585
    iget-object p0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz p0, :cond_0

    .line 2586
    invoke-interface {p0}, Lcom/mxnavi/busines/ExtendServiceInterface;->getPageId()I

    move-result p0

    invoke-interface {p1, p0}, Lcom/example/mxextend/listener/IPageChangedListener;->onPageChanged(I)V

    :cond_0
    return-void
.end method

.method private static final backToMap$lambda$30(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2229
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final calculateRoad$lambda$29(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2162
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final cancelNavigation$lambda$13(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1361
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u53d6\u6d88\u5bfc\u822a 1========================3"

    .line 1360
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1364
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final carltdBindRequest$lambda$5(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 723
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 722
    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final carltdCheckBindRequest$lambda$3(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 627
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u8f66\u4f01\u8d26\u53f7\u7ed1\u5b9a\u68c0\u67e5\u8bf7\u6c42========================3"

    .line 626
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 630
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final carltdUnBindRequest$lambda$4(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 676
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 675
    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final changeNaviRoutePrefer$lambda$23(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2

    const/16 v0, 0x2710

    if-ne p0, v0, :cond_0

    .line 1763
    new-instance p0, Lcom/example/mxextend/entity/ExtendBaseModel;

    invoke-direct {p0}, Lcom/example/mxextend/entity/ExtendBaseModel;-><init>()V

    invoke-interface {p1, p0}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_0

    .line 1765
    :cond_0
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const-string v1, ""

    invoke-direct {v0, p0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :goto_0
    return-void
.end method

.method private static final changeNaviRoutePrefer$lambda$24(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1772
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1771
    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final collectByPoi$lambda$20(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1623
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final deleteViaPoint$lambda$51(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 3083
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u5220\u9664\u9014\u7ecf\u70b9 ========================3"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 3084
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final getCityInfo$lambda$27(Lcom/mxnavi/busines/entity/ResponseData;Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 6

    const-string v0, "this$0"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const/4 v0, 0x0

    if-eqz p0, :cond_0

    .line 2098
    invoke-virtual {p0}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v1

    const/16 v2, 0x2710

    if-ne v1, v2, :cond_0

    const/4 v0, 0x1

    :cond_0
    if-eqz v0, :cond_1

    .line 2099
    iget-object v0, p1, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {p0}, Lcom/mxnavi/busines/entity/ResponseData;->getData()Ljava/lang/String;

    move-result-object p0

    const-class v1, Lcom/example/mxextend/entity/CityInfo;

    invoke-virtual {v0, p0, v1}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Lcom/example/mxextend/entity/CityInfo;

    .line 2100
    invoke-virtual {p0}, Lcom/example/mxextend/entity/CityInfo;->getLongitude()D

    move-result-wide v1

    invoke-virtual {p0}, Lcom/example/mxextend/entity/CityInfo;->getLatitude()D

    move-result-wide v3

    new-instance v0, Lcom/example/mxextend/ExtendApi$getCityInfo$1$1;

    invoke-direct {v0, p0, p2}, Lcom/example/mxextend/ExtendApi$getCityInfo$1$1;-><init>(Lcom/example/mxextend/entity/CityInfo;Lcom/example/mxextend/listener/IExtendCallback;)V

    move-object v5, v0

    check-cast v5, Lcom/example/mxextend/listener/IExtendCallback;

    move-object v0, p1

    invoke-virtual/range {v0 .. v5}, Lcom/example/mxextend/ExtendApi;->getAddressByCoordinate(DDLcom/example/mxextend/listener/IExtendCallback;)V

    goto :goto_0

    .line 2115
    :cond_1
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "getCityInfo: \u57ce\u5e02\u83b7\u53d6\u5931\u8d25"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2116
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    invoke-static {p0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p0}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result p0

    const-string v0, ""

    invoke-direct {p1, p0, v0}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :goto_0
    return-void
.end method

.method private static final getCityInfo$lambda$28(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2123
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final getEndPoint$lambda$50(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    const-string v0, "$callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 3014
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u83b7\u53d6\u7ec8\u70b9 1========================3"

    .line 3013
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 3017
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final getFavoriteList$lambda$46(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2823
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final getHomeOrCompanyData$lambda$41(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    const-string v0, "$callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 2633
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 2632
    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final getMXnaviAppLoginResult$lambda$8(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1016
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1015
    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final getRouteSummaryList$lambda$47(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2866
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final getSearchHistoryData$lambda$45(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2779
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final goHomeOrCompany$lambda$48(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2

    const/16 v0, 0x2710

    if-ne p0, v0, :cond_0

    .line 2881
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    new-instance p0, Lcom/example/mxextend/entity/ExtendBaseModel;

    invoke-direct {p0}, Lcom/example/mxextend/entity/ExtendBaseModel;-><init>()V

    invoke-interface {p1, p0}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_0

    .line 2883
    :cond_0
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const-string v1, ""

    invoke-direct {v0, p0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :goto_0
    return-void
.end method

.method private static final goHomeOrCompany$lambda$49(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2889
    invoke-static {p0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final goSetting$lambda$21(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2

    const/16 v0, 0x2710

    if-ne p0, v0, :cond_0

    .line 1718
    new-instance p0, Lcom/example/mxextend/entity/ExtendBaseModel;

    invoke-direct {p0}, Lcom/example/mxextend/entity/ExtendBaseModel;-><init>()V

    invoke-interface {p1, p0}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_0

    .line 1720
    :cond_0
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const-string v1, ""

    invoke-direct {v0, p0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :goto_0
    return-void
.end method

.method private static final goSetting$lambda$22(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1726
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    .line 1727
    sget-object p0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "goSetting:exception"

    invoke-static {p0, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void
.end method

.method private static final goToNearbyGasStation$lambda$12(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1296
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1295
    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private final initDisplaySurfaceView(I)V
    .locals 4

    .line 940
    new-instance v0, Lcom/example/mxextend/widget/config/SurfaceMapConfig;

    invoke-direct {v0}, Lcom/example/mxextend/widget/config/SurfaceMapConfig;-><init>()V

    const/16 v1, 0x780

    .line 941
    invoke-virtual {v0, v1}, Lcom/example/mxextend/widget/config/SurfaceMapConfig;->setMapWidth(I)V

    const/16 v1, 0x2d0

    .line 942
    invoke-virtual {v0, v1}, Lcom/example/mxextend/widget/config/SurfaceMapConfig;->setMapHeight(I)V

    .line 943
    new-instance v1, Lcom/example/mxextend/widget/DisplaySurfaceView;

    iget-object v2, p0, Lcom/example/mxextend/ExtendApi;->context:Landroid/content/Context;

    iget-object v3, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-direct {v1, v2, p1, v0, v3}, Lcom/example/mxextend/widget/DisplaySurfaceView;-><init>(Landroid/content/Context;ILcom/example/mxextend/widget/config/SurfaceMapConfig;Lcom/mxnavi/busines/ExtendServiceInterface;)V

    iput-object v1, p0, Lcom/example/mxextend/ExtendApi;->displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

    .line 944
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "initDisplaySurfaceView: displaySurfaceView is Bind"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 945
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Lcom/example/mxextend/widget/DisplaySurfaceView;->onBindSurfaceViewListener()V

    :cond_0
    return-void
.end method

.method private static final isExistInCollect$lambda$42(Lcom/mxnavi/busines/entity/ResponseData;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2670
    invoke-virtual {p0}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v0

    const/16 v1, 0x2710

    if-ne v0, v1, :cond_0

    const/4 v0, -0x1

    .line 2672
    new-instance v1, Lcom/example/mxextend/entity/CollectInfoModel;

    invoke-direct {v1}, Lcom/example/mxextend/entity/CollectInfoModel;-><init>()V

    .line 2674
    :try_start_0
    invoke-virtual {p0}, Lcom/mxnavi/busines/entity/ResponseData;->getData()Ljava/lang/String;

    move-result-object p0

    invoke-static {p0}, Ljava/lang/Integer;->valueOf(Ljava/lang/String;)Ljava/lang/Integer;

    move-result-object p0

    const-string v2, "valueOf(data.data)"

    invoke-static {p0, v2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast p0, Ljava/lang/Number;

    invoke-virtual {p0}, Ljava/lang/Number;->intValue()I

    move-result v0
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 2677
    :catch_0
    invoke-virtual {v1, v0}, Lcom/example/mxextend/entity/CollectInfoModel;->setCollcetType(I)V

    .line 2678
    check-cast v1, Lcom/example/mxextend/entity/ExtendBaseModel;

    invoke-interface {p1, v1}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_0

    .line 2680
    :cond_0
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    invoke-virtual {p0}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result p0

    const-string v1, ""

    invoke-direct {v0, p0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :goto_0
    return-void
.end method

.method private static final isExistInCollect$lambda$43(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2687
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method public static synthetic lambda$08EnaLaqgGR_ZyLz1U2OztdQun4(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/example/mxextend/ExtendApi;->setSpecialPoi$lambda$18(ILcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$1916dJdq-i93UscU54lrsKYGe70(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/example/mxextend/ExtendApi;->setVolumeMute$lambda$34(ILcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$1a22TnSv_aaqzsSw7lDkAgfObN0(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->requestRouteEx$lambda$36(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$2-7WakkNQXVaLDYEOVuGrEEMjvI(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/example/mxextend/ExtendApi;->goSetting$lambda$21(ILcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$2NJoxykdtL2O8zF48EMmJwUirW4(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->setSpecialPoi$lambda$19(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$2aVXog2AHb6iLiDob_tJ9xh2P3U(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->navigationPlanningRoute$lambda$2(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$3MEdjZC5oqPrbHErr3OExHDz-iU(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->searchPoi$lambda$9(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$72mGeetv74cM_B2p4JsUs4ole-E(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->pageOprea$lambda$31(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$8dIyYN5BKAGy16vDq1oqqlQjPVY(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->carltdBindRequest$lambda$5(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$A1T-CR88F-E93uBmrqA70a61Lgg(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->changeNaviRoutePrefer$lambda$24(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$Aqv5lO8L4oGRWYRpRdmd5RGjF1A(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->requestGuideInfo$lambda$38(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$AwhGZxBWZ0tcr63oDKbGWVkGfW4(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->goToNearbyGasStation$lambda$12(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$BMCtDqCvs--s2bjUZxtkU84JjW4(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->deleteViaPoint$lambda$51(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$ELQB3Uyc4jjKS27IfR6zTZK3JME(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->carltdCheckBindRequest$lambda$3(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$EgMRbCufnICbj_xAHKvD6Vlha80(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->getMXnaviAppLoginResult$lambda$8(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$EpLh7gNLWsupufbA0z1It5d1XnA(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->searchTraffic$lambda$14(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$GdyGxWGvuY8A9qkBGxbo17D9w64(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->getFavoriteList$lambda$46(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$GrilVj59MAXTGjU6DVt44GlDRSA(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->carltdUnBindRequest$lambda$4(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$LnpTUBLElZpoiLHv0XaUlwGHDiE(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->getHomeOrCompanyData$lambda$41(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$M6oHhz7rOm-FWq2lwnBmU2qwSa0(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->requestAlongRouteData$lambda$26(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$OKuvV6js9eOkU04qBfNmRqOBV3k(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->isExistInCollect$lambda$43(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$RJTFyQK3vi2jP3mn23eiLFuOMHg(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->goSetting$lambda$22(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$RUEuj1UTosJ5cZyQf3XH93NnHqM(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->goHomeOrCompany$lambda$49(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$Ro6qzG3s85Xr2ZX3CyWZY2xIWc8(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->startMXnaviAppResult$lambda$6(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$Tx8gSkeSCCoE873V8Jw5FqrjUhU(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/example/mxextend/ExtendApi;->goHomeOrCompany$lambda$48(ILcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$ZhTIF0MSncd3l_sekFFJEKYD34s(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->getEndPoint$lambda$50(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$_wDEZkqejzE2JGYnaZbl9CqMcTs(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->setCompanyAddressResult$lambda$1(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$alOVmsoLhEbysWCxlVKRaMQTJeI(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->setVolumeMute$lambda$35(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$cno7kFLHMp3ZLMv0LOUj_aNfhsE(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->setHomeAddressResult$lambda$0(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$daBXJiV0edpoD8nQo-scEWiHQms(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->setNaviScreen$lambda$44(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$eSlLPjVD9HhdayhQT1M6TTTPiL4(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/example/mxextend/ExtendApi;->specialPoiNavi$lambda$15(ILcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$i8epPa3lsX24Qeq094Ty46zSpv4(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->specialPoiNavi$lambda$17(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$iz9IoCIt4z-vI9l8tNqsQSRS7y8(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->pageOprea$lambda$33(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$jx8KNqpHKw_CtBI08Sf7X91tCTY(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->collectByPoi$lambda$20(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$lTBpFJNTOSIILSie0TzfqbjAIGc(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->cancelNavigation$lambda$13(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$mTl5V0SID9lDpc3Mqy7qOQAyEGE(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->getSearchHistoryData$lambda$45(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$mmK8RCObg26q_ucKruM5IUmD7EE(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->calculateRoad$lambda$29(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$p9LANwqGTzkjE0NGkwCKvIFySB8(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->naviToPoi$lambda$10(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$pquT3opnDkalQlY96exjcRJrdzg(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->searchAlongRoute$lambda$25(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$qOwok-Gdf864kUFSf0JN4tHzAHY(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->getRouteSummaryList$lambda$47(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$rHBdx2uKBzM6LRur2yFxZUTKRiY(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->requestAddPass$lambda$37(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$sxIM9IZ-SeegTofKHHwFY9z3dgY(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IPageChangedListener;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/example/mxextend/ExtendApi;->addPageChangedListener$lambda$40(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IPageChangedListener;)V

    return-void
.end method

.method public static synthetic lambda$tB8HAbkV1qw8-o8tSeLljkatirE(Lcom/mxnavi/busines/entity/ResponseData;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/example/mxextend/ExtendApi;->isExistInCollect$lambda$42(Lcom/mxnavi/busines/entity/ResponseData;Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$tH3PBIKg4obKrqDlXhPSMBMtdwM(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->getCityInfo$lambda$28(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$v9G6eqSjajYD-o4KeL1DDApjk8s(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/example/mxextend/ExtendApi;->pageOprea$lambda$32(ILcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$w6cNHQrygmXGU99R1f9tHFpseVI(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0, p1}, Lcom/example/mxextend/ExtendApi;->changeNaviRoutePrefer$lambda$23(ILcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$x-vDT2SCrmGGfcMz-Tqudz01r5c(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->navigation$lambda$11(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$x22qV_-qeEBJIEpRSa2F7A8vjC0(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->backToMap$lambda$30(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$xQxq0x7LqjWDvUNM72TbL9Nb7gc(Lcom/mxnavi/busines/entity/ResponseData;Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0, p1, p2}, Lcom/example/mxextend/ExtendApi;->getCityInfo$lambda$27(Lcom/mxnavi/busines/entity/ResponseData;Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public static synthetic lambda$zg7DebM4MhEGr-5XQQY970K9uVU(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0

    invoke-static {p0}, Lcom/example/mxextend/ExtendApi;->specialPoiNavi$lambda$16(Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method private static final naviToPoi$lambda$10(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1188
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u9519\u8bef"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1187
    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final navigation$lambda$11(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1240
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u9519\u8bef"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1239
    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final navigationPlanningRoute$lambda$2(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 578
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u9884\u7ea6\u5bfc\u822a \u5bfc\u822a\u89c4\u5212\u8def\u7ebf========================3"

    .line 577
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 581
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final pageOprea$lambda$31(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2274
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final pageOprea$lambda$32(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2

    const/16 v0, 0x2710

    if-ne p0, v0, :cond_0

    .line 2323
    new-instance p0, Lcom/example/mxextend/entity/ExtendBaseModel;

    invoke-direct {p0}, Lcom/example/mxextend/entity/ExtendBaseModel;-><init>()V

    invoke-interface {p1, p0}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_0

    :cond_0
    const/16 v0, 0x277e

    if-eq p0, v0, :cond_1

    .line 2325
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const-string v1, ""

    invoke-direct {v0, p0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :cond_1
    :goto_0
    return-void
.end method

.method private static final pageOprea$lambda$33(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2332
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private final removeSelfFromParent(Landroid/view/View;)V
    .locals 4

    if-eqz p1, :cond_0

    .line 933
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    instance-of v0, v0, Landroid/view/ViewGroup;

    if-eqz v0, :cond_0

    .line 934
    invoke-virtual {p1}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    move-result-object v0

    const-string v1, "null cannot be cast to non-null type android.view.ViewGroup"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Landroid/view/ViewGroup;

    .line 935
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "removeSelfFromParent: parent:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 936
    invoke-virtual {v0, p1}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    :cond_0
    return-void
.end method

.method private final removeStatusChangedListener()V
    .locals 2

    .line 287
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    return-void

    .line 290
    :cond_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->statusChangedListener:Lcom/mxnavi/busines/IStatusChangedListener$Stub;

    check-cast v1, Lcom/mxnavi/busines/IStatusChangedListener;

    invoke-interface {v0, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->removeStatusChangedListener(Lcom/mxnavi/busines/IStatusChangedListener;)V

    .line 291
    :cond_1
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "removeStatusChangedListener"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 293
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    :goto_0
    return-void
.end method

.method private static final requestAddPass$lambda$37(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2448
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final requestAlongRouteData$lambda$26(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2004
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final requestGuideInfo$lambda$38(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2487
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "requestGuideInfo: exception="

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2488
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final requestRouteEx$lambda$36(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2412
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final searchAlongRoute$lambda$25(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1960
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final searchPoi$lambda$9(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1108
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private final searchTraffic(ILjava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/TrafficInfoModel;",
            ">;)V"
        }
    .end annotation

    if-nez p3, :cond_0

    .line 1438
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "searchTraffic:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 1440
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 1441
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    .line 1444
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$searchTraffic$1;

    invoke-direct {v1, p0, p3}, Lcom/example/mxextend/ExtendApi$searchTraffic$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, p1, p2, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->searchTraffic(ILjava/lang/String;Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 1457
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1458
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance p2, Lcom/example/mxextend/-$$Lambda$ExtendApi$EpLh7gNLWsupufbA0z1It5d1XnA;

    invoke-direct {p2, p3}, Lcom/example/mxextend/-$$Lambda$ExtendApi$EpLh7gNLWsupufbA0z1It5d1XnA;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, p2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method private static final searchTraffic$lambda$14(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1459
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "searchTraffic: exception"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1460
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u9519\u8bef"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final setCompanyAddressResult$lambda$1(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    const-string v0, "$callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 509
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 508
    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final setHomeAddressResult$lambda$0(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    const-string v0, "$callback"

    invoke-static {p0, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 460
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 459
    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final setNaviScreen$lambda$44(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2715
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, "\u8bf7\u6c42\u53d1\u751f\u5f02\u5e38"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final setSpecialPoi$lambda$18(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2

    const/16 v0, 0x2710

    if-ne p0, v0, :cond_0

    .line 1579
    new-instance p0, Lcom/example/mxextend/entity/ExtendBaseModel;

    invoke-direct {p0}, Lcom/example/mxextend/entity/ExtendBaseModel;-><init>()V

    invoke-interface {p1, p0}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_0

    .line 1581
    :cond_0
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const-string v1, ""

    invoke-direct {v0, p0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :goto_0
    return-void
.end method

.method private static final setSpecialPoi$lambda$19(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1588
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private final setStatusChangedListener()V
    .locals 2

    .line 272
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    return-void

    .line 275
    :cond_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->statusChangedListener:Lcom/mxnavi/busines/IStatusChangedListener$Stub;

    check-cast v1, Lcom/mxnavi/busines/IStatusChangedListener;

    invoke-interface {v0, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->addStatusChangedListener(Lcom/mxnavi/busines/IStatusChangedListener;)V

    .line 276
    :cond_1
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "setStatusChangedListener"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 278
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    :goto_0
    return-void
.end method

.method private static final setVolumeMute$lambda$34(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2

    const/16 v0, 0x2710

    if-ne p0, v0, :cond_0

    .line 2352
    new-instance p0, Lcom/example/mxextend/entity/ExtendBaseModel;

    invoke-direct {p0}, Lcom/example/mxextend/entity/ExtendBaseModel;-><init>()V

    invoke-interface {p1, p0}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_0

    .line 2354
    :cond_0
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const-string v1, ""

    invoke-direct {v0, p0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :goto_0
    return-void
.end method

.method private static final setVolumeMute$lambda$35(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 2361
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final specialPoiNavi$lambda$15(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2

    const/16 v0, 0x2710

    if-ne p0, v0, :cond_0

    .line 1516
    new-instance p0, Lcom/example/mxextend/entity/ExtendBaseModel;

    invoke-direct {p0}, Lcom/example/mxextend/entity/ExtendBaseModel;-><init>()V

    invoke-interface {p1, p0}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_0

    .line 1518
    :cond_0
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const-string v1, ""

    invoke-direct {v0, p0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :goto_0
    return-void
.end method

.method private static final specialPoiNavi$lambda$16(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1525
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final specialPoiNavi$lambda$17(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 1557
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private static final startMXnaviAppResult$lambda$6(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3

    .line 777
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u62c9\u8d77\u5bfc\u822aapp========================3"

    .line 776
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 780
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    const-string v2, ""

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p0, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method private final declared-synchronized unbindService(Landroid/content/ServiceConnection;)V
    .locals 3

    monitor-enter p0

    .line 211
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    .line 213
    :try_start_1
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->context:Landroid/content/Context;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0, p1}, Landroid/content/Context;->unbindService(Landroid/content/ServiceConnection;)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 215
    :try_start_2
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "unbindService error: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {p1}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    :goto_0
    const/4 p1, 0x0

    .line 217
    iput-boolean p1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 219
    :cond_0
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0

    throw p1
.end method


# virtual methods
.method public accountOpera(I)I
    .locals 4

    .line 2010
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 p1, -0x1

    return p1

    .line 2014
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0, p1}, Lcom/mxnavi/busines/ExtendServiceInterface;->accountOpera(I)I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 2016
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 v0, -0x2

    .line 2019
    :goto_1
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "accountopera:action="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v2, "resp= "

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v2, ""

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v1, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0
.end method

.method public addAccountChangedListener(Lcom/example/mxextend/listener/IAccountListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 1484
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->accountListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 1485
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->accountListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public addExtendListener(Lcom/example/mxextend/listener/IExtendListener;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    .line 880
    :cond_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/CopyOnWriteArrayList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 881
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/CopyOnWriteArrayList;->add(Ljava/lang/Object;)Z

    :cond_1
    return-void
.end method

.method public addHomeOrCompanyChangedListener(Lcom/example/mxextend/listener/IHomeOrCompanyListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2911
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->homeOrCompanyListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2912
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->homeOrCompanyListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public addLocationChangedListener(Lcom/example/mxextend/listener/ILocationChangedListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2496
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->locationListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2497
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->locationListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public addLocationUpdateCallBack(Lcom/example/mxextend/listener/ILocationUpdateCallBack;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2566
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->locationUpdateCallBacks:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2567
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->locationUpdateCallBacks:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public addManualVoiceInteractionListener(Lcom/example/mxextend/listener/IManualVoiceInteractionListener;)V
    .locals 2

    if-eqz p1, :cond_1

    .line 3211
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->manualVoiceInteractionListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 3212
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->manualVoiceInteractionListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 3214
    :cond_0
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->manualVoiceInteractionListeners:Ljava/util/LinkedList;

    if-eqz p1, :cond_1

    .line 3215
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "==========\u6ce8\u518c\u624b\u52a8\u8bed\u97f3\u4ea4\u4e92\u76d1\u542c========"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->manualVoiceInteractionListeners:Ljava/util/LinkedList;

    invoke-virtual {v1}, Ljava/util/LinkedList;->size()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :cond_1
    return-void
.end method

.method public addPageChangedListener(Lcom/example/mxextend/listener/IPageChangedListener;)V
    .locals 3

    if-eqz p1, :cond_1

    .line 2580
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->pageChangedListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2581
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->pageChangedListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    .line 2583
    :cond_0
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "==========\u6ce8\u518c\u9875\u9762\u5207\u6362\u76d1\u542c========"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lcom/example/mxextend/ExtendApi;->pageChangedListeners:Ljava/util/LinkedList;

    invoke-virtual {v2}, Ljava/util/LinkedList;->size()I

    move-result v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2584
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$sxIM9IZ-SeegTofKHHwFY9z3dgY;

    invoke-direct {v1, p0, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$sxIM9IZ-SeegTofKHHwFY9z3dgY;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IPageChangedListener;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_1
    return-void
.end method

.method public addRouteDataListener(Lcom/example/mxextend/listener/IRouteDataListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2510
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->routeDataListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2511
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->routeDataListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public addSearchDataListener(Lcom/example/mxextend/listener/ISearchDataListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2524
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->searchDataListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2525
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->searchDataListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public addSearchResultListener(Lcom/example/mxextend/listener/ISearchResultListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2538
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->searchResultListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2539
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->searchResultListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public addServiceConnectedListener(Lcom/example/mxextend/listener/IServiceConnectedListener;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    .line 256
    :cond_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnectedListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0, p1}, Ljava/util/concurrent/CopyOnWriteArrayList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    .line 257
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnectedListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/CopyOnWriteArrayList;->add(Ljava/lang/Object;)Z

    :cond_1
    return-void
.end method

.method public addSuggestDataListener(Lcom/example/mxextend/listener/ISuggestionResultListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2552
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->suggestionResultListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2553
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->suggestionResultListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public addWeatherInfoListenerListener(Lcom/example/mxextend/listener/IWeatherInfoListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2942
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->weatherInfoListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    .line 2943
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->weatherInfoListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->add(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public aroundSearch(Ljava/lang/String;Ljava/lang/String;IFLcom/example/mxextend/listener/IExtendCallback;)I
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "IF",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/SearchResultModel;",
            ">;)I"
        }
    .end annotation

    move-object/from16 v0, p5

    const/4 v1, 0x6

    if-nez v0, :cond_0

    .line 1906
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v2, "aroundSearch:callback= null"

    invoke-static {v0, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v1

    .line 1909
    :cond_0
    new-instance v2, Lcom/mxnavi/busines/entity/RequestSearchData;

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/16 v14, 0x3ff

    const/4 v15, 0x0

    move-object v3, v2

    invoke-direct/range {v3 .. v15}, Lcom/mxnavi/busines/entity/RequestSearchData;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IZIZFIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    move-object/from16 v3, p1

    .line 1910
    invoke-virtual {v2, v3}, Lcom/mxnavi/busines/entity/RequestSearchData;->setNearbyKey(Ljava/lang/String;)V

    move-object/from16 v3, p2

    .line 1911
    invoke-virtual {v2, v3}, Lcom/mxnavi/busines/entity/RequestSearchData;->setSearchKey(Ljava/lang/String;)V

    const/4 v3, 0x1

    .line 1912
    invoke-virtual {v2, v3}, Lcom/mxnavi/busines/entity/RequestSearchData;->setNearBySearch(Z)V

    .line 1913
    invoke-virtual {v2, v3}, Lcom/mxnavi/busines/entity/RequestSearchData;->setNeedShow(Z)V

    move/from16 v3, p3

    .line 1914
    invoke-virtual {v2, v3}, Lcom/mxnavi/busines/entity/RequestSearchData;->setSearchAction(I)V

    move/from16 v3, p4

    .line 1915
    invoke-virtual {v2, v3}, Lcom/mxnavi/busines/entity/RequestSearchData;->setDistance(F)V

    move-object/from16 v3, p0

    .line 1916
    invoke-virtual {v3, v2, v0}, Lcom/example/mxextend/ExtendApi;->searchPoi(Lcom/mxnavi/busines/entity/RequestSearchData;Lcom/example/mxextend/listener/IExtendCallback;)V

    return v1
.end method

.method public backToMap(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 2207
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "backToMap:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 2208
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 2209
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    const-string v1, ""

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    goto :goto_0

    .line 2212
    :cond_1
    :try_start_0
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "=============backToMap==============3.6.5"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2213
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$backToMap$1;

    invoke-direct {v1, p0, p2}, Lcom/example/mxextend/ExtendApi$backToMap$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, p1, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->backToMap(ILcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 2227
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2228
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "backToMap: exception"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2229
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$x22qV_-qeEBJIEpRSa2F7A8vjC0;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$x22qV_-qeEBJIEpRSa2F7A8vjC0;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public bindMxExtService()V
    .locals 4

    .line 233
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-eqz v0, :cond_0

    .line 234
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "=============\u5df2\u7ecf\u7ed1\u5b9a\u670d\u52a1\u8fc7============"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 237
    :cond_0
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "=============bindMx ExtService============aar\u7248\u672c\u53f7 = 1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->i(Ljava/lang/String;Ljava/lang/String;)I

    .line 238
    new-instance v0, Landroid/content/Intent;

    const-string v1, "com.mxnavi.remote_service"

    invoke-direct {v0, v1}, Landroid/content/Intent;-><init>(Ljava/lang/String;)V

    const-string v1, "cn.loopon.app.navi"

    .line 239
    invoke-virtual {v0, v1}, Landroid/content/Intent;->setPackage(Ljava/lang/String;)Landroid/content/Intent;

    .line 240
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->context:Landroid/content/Context;

    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    iget-object v2, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnection:Landroid/content/ServiceConnection;

    const/4 v3, 0x1

    invoke-virtual {v1, v0, v2, v3}, Landroid/content/Context;->bindService(Landroid/content/Intent;Landroid/content/ServiceConnection;I)Z

    return-void
.end method

.method public calculateRoad(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/RouteResult;",
            ">;)V"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 2130
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "calculateRoad:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 2133
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 2134
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    const-string v1, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 2138
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$calculateRoad$1;

    invoke-direct {v1, p0, p2}, Lcom/example/mxextend/ExtendApi$calculateRoad$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, p1, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->calculateRoad(ILcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 2159
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2160
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "calculateRoad: exception"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2161
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$mmK8RCObg26q_ucKruM5IUmD7EE;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$mmK8RCObg26q_ucKruM5IUmD7EE;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public cancelNavi()I
    .locals 4

    .line 1467
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    goto :goto_2

    .line 1472
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->cancelNavi()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 1474
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 v0, -0x2

    .line 1477
    :goto_1
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "cancelNavi: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :goto_2
    return v0
.end method

.method public cancelNaviBack()I
    .locals 4

    .line 3090
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-nez v0, :cond_0

    goto :goto_1

    .line 3094
    :cond_0
    :try_start_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->cancelNaviBack()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 3096
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 v0, -0x2

    .line 3099
    :goto_0
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "cancelNaviBack: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    :cond_1
    :goto_1
    const/4 v0, -0x1

    return v0
.end method

.method public cancelNavigation(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    .line 1321
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================cancelNavigation\u53d6\u6d88\u5bfc\u822a========================1.0.31"

    .line 1320
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    if-nez p1, :cond_0

    const-string p1, "\u53d6\u6d88\u5bfc\u822a:callback= null"

    .line 1325
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 1329
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    .line 1331
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u53d6\u6d88\u5bfc\u822a \u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1330
    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    :cond_1
    :try_start_0
    const-string v1, "======================\u53d6\u6d88\u5bfc\u822a========================1"

    .line 1339
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1340
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$cancelNavigation$1;

    invoke-direct {v1, p0, p1}, Lcom/example/mxextend/ExtendApi$cancelNavigation$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->cancelNavigation(Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 1357
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1358
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u53d6\u6d88\u5bfc\u822a========================2"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1359
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$lTBpFJNTOSIILSie0TzfqbjAIGc;

    invoke-direct {v1, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$lTBpFJNTOSIILSie0TzfqbjAIGc;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public carltdBindRequest(Ljava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/CarltdRequestInfoModel;",
            ">;)V"
        }
    .end annotation

    .line 689
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "\u7ed1\u5b9a\uff1a"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "======================\u7533\u8bf7\u8d26\u53f7\u7ed1\u5b9a========================1.0.31"

    .line 690
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 691
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_0

    return-void

    :cond_0
    if-nez p2, :cond_1

    const-string p1, "\u8f66\u4f01\u8d26\u53f7\u7533\u8bf7\u8d26\u53f7\u7ed1\u5b9a\u8bf7\u6c42:callback= null"

    .line 695
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 699
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    .line 701
    new-instance v1, Lcom/example/mxextend/ExtendApi$carltdBindRequest$1;

    invoke-direct {v1, p0, p2}, Lcom/example/mxextend/ExtendApi$carltdBindRequest$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    .line 699
    invoke-interface {v0, p1, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->carltdBindRequest(Ljava/lang/String;Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 719
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 720
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "requestAddPass: exception="

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 721
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$8dIyYN5BKAGy16vDq1oqqlQjPVY;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$8dIyYN5BKAGy16vDq1oqqlQjPVY;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public carltdCheckBindRequest(Ljava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/CarltdRequestInfoModel;",
            ">;)V"
        }
    .end annotation

    .line 590
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u8f66\u4f01\u8d26\u53f7\u7ed1\u5b9a\u68c0\u67e5\u8bf7\u6c42========================1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    if-nez p2, :cond_0

    const-string p1, "\u8f66\u4f01\u8d26\u53f7\u7ed1\u5b9a\u68c0\u67e5\u8bf7\u6c42:callback= null"

    .line 592
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 595
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    const-string p1, "\u8f66\u4f01\u8d26\u53f7\u7ed1\u5b9a\u68c0\u67e5\u8bf7\u6c42:\u670d\u52a1\u6ca1\u6709\u7ed1\u5b9a"

    .line 596
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 597
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    const-string v1, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    :cond_1
    const-string v1, "======================\u8f66\u4f01\u8d26\u53f7\u7ed1\u5b9a\u68c0\u67e5\u8bf7\u6c42========================1"

    .line 600
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 602
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    .line 604
    new-instance v1, Lcom/example/mxextend/ExtendApi$carltdCheckBindRequest$1;

    invoke-direct {v1, p0, p2}, Lcom/example/mxextend/ExtendApi$carltdCheckBindRequest$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    .line 602
    invoke-interface {v0, p1, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->carltdCheckBindRequest(Ljava/lang/String;Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 622
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u8f66\u4f01\u8d26\u53f7\u7ed1\u5b9a\u68c0\u67e5\u8bf7\u6c42========================2"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 623
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    const-string p1, "requestAddPass: exception="

    .line 624
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 625
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$ELQB3Uyc4jjKS27IfR6zTZK3JME;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$ELQB3Uyc4jjKS27IfR6zTZK3JME;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public carltdLoginRequest(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/CarltdRequestInfoModel;",
            ">;)V"
        }
    .end annotation

    return-void
.end method

.method public carltdUnBindRequest(Ljava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/CarltdRequestInfoModel;",
            ">;)V"
        }
    .end annotation

    .line 643
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u89e3\u9664\u8d26\u53f7\u7ed1\u5b9a========================1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 644
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_0

    return-void

    :cond_0
    if-nez p2, :cond_1

    const-string p1, "\u8f66\u4f01\u8d26\u53f7\u89e3\u9664\u8d26\u53f7\u7ed1\u5b9a\u8bf7\u6c42:callback= null"

    .line 648
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 652
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    .line 654
    new-instance v1, Lcom/example/mxextend/ExtendApi$carltdUnBindRequest$1;

    invoke-direct {v1, p0, p2}, Lcom/example/mxextend/ExtendApi$carltdUnBindRequest$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    .line 652
    invoke-interface {v0, p1, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->carltdUnBindRequest(Ljava/lang/String;Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 672
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 673
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "requestAddPass: exception="

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 674
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$GrilVj59MAXTGjU6DVt44GlDRSA;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$GrilVj59MAXTGjU6DVt44GlDRSA;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public changeInstrumentShowMode(I)V
    .locals 3

    .line 3104
    iput p1, p0, Lcom/example/mxextend/ExtendApi;->mShowMode:I

    .line 3105
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-nez v0, :cond_0

    .line 3106
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "changeInstrumentShowMode: extendServiceInterface is Null"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 3109
    :cond_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

    if-nez v0, :cond_1

    .line 3110
    invoke-direct {p0, p1}, Lcom/example/mxextend/ExtendApi;->initDisplaySurfaceView(I)V

    .line 3113
    :cond_1
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    .line 3114
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "changeInstrumentShowMode:lastShowMode:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lcom/example/mxextend/ExtendApi;->displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

    invoke-static {v2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v2}, Lcom/example/mxextend/widget/DisplaySurfaceView;->getShowMode()I

    move-result v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " currentShowMode:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 3112
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 3117
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-interface {v0, p1}, Lcom/mxnavi/busines/ExtendServiceInterface;->setInstrumentShowMode(I)V

    .line 3118
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0}, Lcom/example/mxextend/widget/DisplaySurfaceView;->getShowMode()I

    move-result v0

    if-eq v0, p1, :cond_2

    .line 3119
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0, p1}, Lcom/example/mxextend/widget/DisplaySurfaceView;->changeShowMode(I)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 3123
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    :cond_2
    :goto_0
    return-void
.end method

.method public changeNaviRoutePrefer(ILcom/example/mxextend/listener/IExtendCallback;)I
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/16 v0, 0xe

    if-nez p2, :cond_0

    .line 1750
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "changeNaviRoutePrefer:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 1753
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    .line 1754
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 1758
    :cond_1
    :try_start_0
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "changeNaviRoutePrefer:req strategy="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1759
    iget-object v2, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v2, :cond_2

    invoke-interface {v2, p1}, Lcom/mxnavi/busines/ExtendServiceInterface;->changePreference(I)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    :goto_0
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 1760
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "changeNaviRoutePrefer: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1761
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v2, Lcom/example/mxextend/-$$Lambda$ExtendApi$w6cNHQrygmXGU99R1f9tHFpseVI;

    invoke-direct {v2, p1, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$w6cNHQrygmXGU99R1f9tHFpseVI;-><init>(ILcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v1, v2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p1

    .line 1769
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1770
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$A1T-CR88F-E93uBmrqA70a61Lgg;

    invoke-direct {v1, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$A1T-CR88F-E93uBmrqA70a61Lgg;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    .line 1778
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "changeNaviRoutePrefer: exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :goto_1
    return v0
.end method

.method public changePreference(I)I
    .locals 3

    .line 1734
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 p1, -0x1

    return p1

    .line 1739
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0, p1}, Lcom/mxnavi/busines/ExtendServiceInterface;->changePreference(I)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    :goto_0
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p1

    .line 1741
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 p1, -0x2

    .line 1744
    :goto_1
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "changePreference: resultcode="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return p1
.end method

.method public collectByPoi(ILcom/example/mxextend/entity/LocationInfo;Lcom/example/mxextend/listener/IExtendCallback;)I
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/entity/LocationInfo;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/16 v0, 0x14

    if-nez p3, :cond_0

    .line 1596
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "collectByPoi:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 1599
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    .line 1600
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 p2, -0x1

    const-string v1, ""

    invoke-direct {p1, p2, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 1603
    :cond_1
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "collectByPoi: type="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    const-string v3, "  info="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1605
    :try_start_0
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v1, :cond_2

    iget-object v2, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {v2, p2}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    .line 1606
    new-instance v2, Lcom/example/mxextend/ExtendApi$collectByPoi$1;

    invoke-direct {v2, p0, p3}, Lcom/example/mxextend/ExtendApi$collectByPoi$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v2, Lcom/mxnavi/busines/IServiceCallBack;

    .line 1605
    invoke-interface {v1, p1, p2, v2}, Lcom/mxnavi/busines/ExtendServiceInterface;->collectByPoi(ILjava/lang/String;Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 1620
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1621
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "collectByPoi: exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1622
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance p2, Lcom/example/mxextend/-$$Lambda$ExtendApi$jx8KNqpHKw_CtBI08Sf7X91tCTY;

    invoke-direct {p2, p3}, Lcom/example/mxextend/-$$Lambda$ExtendApi$jx8KNqpHKw_CtBI08Sf7X91tCTY;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, p2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return v0
.end method

.method public deleteViaPoint(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/LocationInfo;",
            ">;)V"
        }
    .end annotation

    .line 3049
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================deleteViaPoint\u5220\u9664\u9014\u7ecf\u70b9========================1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    if-nez p2, :cond_0

    const-string p1, "deleteViaPoint\u5220\u9664\u9014\u7ecf\u70b9:callback= null"

    .line 3051
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 3055
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    .line 3056
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    const-string v1, "\u5220\u9664\u9014\u7ecf\u70b9 \u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    :cond_1
    :try_start_0
    const-string v1, "======================\u5220\u9664\u9014\u7ecf\u70b9========================1"

    .line 3060
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 3061
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$deleteViaPoint$1;

    invoke-direct {v1, p0, p2}, Lcom/example/mxextend/ExtendApi$deleteViaPoint$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, p1, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->deleteViaPoint(ILcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 3080
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 3081
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "======================\u5220\u9664\u9014\u7ecf\u70b9========================2"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 3082
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$BMCtDqCvs--s2bjUZxtkU84JjW4;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$BMCtDqCvs--s2bjUZxtkU84JjW4;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public doRequest(Ljava/lang/String;Lcom/example/mxextend/listener/IExtendJsonCallback;)V
    .locals 3

    const-string v0, "protocol"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "callback"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 392
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u65b0\u589e\u901a\u7528\u8bf7\u6c42========================"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 393
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "======================isbind========================"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-boolean v2, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, "  "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-boolean v2, p0, Lcom/example/mxextend/ExtendApi;->reconnect:Z

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 394
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->reconnect:Z

    if-nez v1, :cond_0

    const/4 v0, 0x1

    .line 395
    iput-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->reconnect:Z

    .line 396
    iput-object p1, p0, Lcom/example/mxextend/ExtendApi;->cacheProtocol:Ljava/lang/String;

    .line 397
    iput-object p2, p0, Lcom/example/mxextend/ExtendApi;->cacheExtendCallBack:Lcom/example/mxextend/listener/IExtendJsonCallback;

    .line 398
    invoke-virtual {p0}, Lcom/example/mxextend/ExtendApi;->bindMxExtService()V

    return-void

    :cond_0
    if-nez v0, :cond_1

    return-void

    .line 405
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    new-instance v1, Lcom/example/mxextend/ExtendApi$doRequest$1;

    invoke-direct {v1, p0, p2}, Lcom/example/mxextend/ExtendApi$doRequest$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendJsonCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, p1, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->doRequest(Ljava/lang/String;Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 415
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    :goto_0
    return-void
.end method

.method public getAccountStatus()I
    .locals 5

    .line 2054
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    goto :goto_1

    :cond_0
    const/4 v0, -0x2

    .line 2057
    :try_start_0
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v1, :cond_1

    invoke-interface {v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->getAccountStatus()Lcom/mxnavi/busines/entity/ResponseData;

    move-result-object v1

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :goto_0
    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    .line 2058
    sget-object v2, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "getAccountStatus:"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    iget-object v4, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {v4, v1}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2059
    invoke-virtual {v1}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v2

    const/16 v3, 0x2710

    if-eq v2, v3, :cond_2

    return v0

    .line 2062
    :cond_2
    invoke-virtual {v1}, Lcom/mxnavi/busines/entity/ResponseData;->getData()Ljava/lang/String;

    move-result-object v1

    check-cast v1, Ljava/lang/CharSequence;

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    xor-int/lit8 v0, v0, 0x1

    goto :goto_1

    :catch_0
    move-exception v1

    .line 2064
    invoke-virtual {v1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2065
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v2, "getAccountStatus: exception"

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :goto_1
    return v0
.end method

.method public getAddressByCoordinate(DDLcom/example/mxextend/listener/IExtendCallback;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(DD",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/AddressModel;",
            ">;)V"
        }
    .end annotation

    .line 1118
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "==================getAddressByLocation================1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1119
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "getAddressByCoordinate: lon = "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1, p2}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " , lat = "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p3, p4}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " , isBind = "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-boolean v2, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    if-nez p5, :cond_0

    const-string p1, "getAddressByCoordinate: callback = null"

    .line 1121
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 1124
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 1126
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 p2, -0x1

    const-string p3, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, p2, p3}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1125
    invoke-interface {p5, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 1133
    :cond_1
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$getAddressByCoordinate$1;

    invoke-direct {v1, p0, p5}, Lcom/example/mxextend/ExtendApi$getAddressByCoordinate$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    move-object v5, v1

    check-cast v5, Lcom/mxnavi/busines/IServiceCallBack;

    move-wide v1, p1

    move-wide v3, p3

    invoke-interface/range {v0 .. v5}, Lcom/mxnavi/busines/ExtendServiceInterface;->getAddressByCoordinate(DDLcom/mxnavi/busines/IServiceCallBack;)V

    :cond_2
    return-void
.end method

.method public final getCacheExtendCallBack()Lcom/example/mxextend/listener/IExtendJsonCallback;
    .locals 1

    .line 389
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->cacheExtendCallBack:Lcom/example/mxextend/listener/IExtendJsonCallback;

    return-object v0
.end method

.method public final getCacheProtocol()Ljava/lang/String;
    .locals 1

    .line 388
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->cacheProtocol:Ljava/lang/String;

    return-object v0
.end method

.method public getCityInfo(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/CityInfo;",
            ">;)V"
        }
    .end annotation

    if-nez p1, :cond_0

    .line 2087
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "getCityInfo:callback= null"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 2090
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 2091
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 2095
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->getCityInfo()Lcom/mxnavi/busines/entity/ResponseData;

    move-result-object v0

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    .line 2096
    :goto_0
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getCityInfo:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {v3, v0}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2097
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v2, Lcom/example/mxextend/-$$Lambda$ExtendApi$xQxq0x7LqjWDvUNM72TbL9Nb7gc;

    invoke-direct {v2, v0, p0, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$xQxq0x7LqjWDvUNM72TbL9Nb7gc;-><init>(Lcom/mxnavi/busines/entity/ResponseData;Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v1, v2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 2120
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v2, "getCityInfo: exception"

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2121
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2122
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$tH3PBIKg4obKrqDlXhPSMBMtdwM;

    invoke-direct {v1, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$tH3PBIKg4obKrqDlXhPSMBMtdwM;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_1
    return-void
.end method

.method public getDestInfo(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/LocationInfo;",
            ">;)V"
        }
    .end annotation

    if-nez p1, :cond_0

    .line 1785
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "getDestInfo:callback= null"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto/16 :goto_0

    .line 1788
    :cond_0
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 1789
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1790
    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 1793
    :cond_1
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->getDestInfo()Lcom/mxnavi/busines/entity/ResponseData;

    move-result-object v0

    .line 1794
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getDestInfo:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {v3, v0}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1795
    invoke-virtual {v0}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v1

    const/16 v2, 0x2710

    if-ne v1, v2, :cond_2

    .line 1797
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    .line 1798
    invoke-virtual {v0}, Lcom/mxnavi/busines/entity/ResponseData;->getData()Ljava/lang/String;

    move-result-object v0

    const-class v2, Lcom/example/mxextend/entity/LocationInfo;

    .line 1797
    invoke-virtual {v1, v0, v2}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object v0

    const-string v1, "null cannot be cast to non-null type com.example.mxextend.entity.LocationInfo"

    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Lcom/example/mxextend/entity/LocationInfo;

    check-cast v0, Lcom/example/mxextend/entity/ExtendBaseModel;

    .line 1796
    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_0

    .line 1803
    :cond_2
    new-instance v1, Lcom/example/mxextend/entity/ExtendErrorModel;

    invoke-virtual {v0}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v2

    invoke-virtual {v0}, Lcom/mxnavi/busines/entity/ResponseData;->getData()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-direct {v1, v2, v0}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 1806
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1807
    new-instance v1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v2, -0x2

    invoke-virtual {v0}, Landroid/os/RemoteException;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-direct {v1, v2, v0}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1808
    invoke-interface {p1, v1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    .line 1809
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "getDestInfo: getDestInfo"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :goto_0
    return-void
.end method

.method public getEndPoint(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/LocationInfo;",
            ">;)V"
        }
    .end annotation

    const-string v0, "callback"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 2971
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u83b7\u53d6\u7ec8\u70b9========================1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2977
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_0

    .line 2979
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u83b7\u53d6\u7ec8\u70b9 \u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 2978
    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    :cond_0
    :try_start_0
    const-string v1, "======================\u83b7\u53d6\u7ec8\u70b9========================1"

    .line 2987
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2988
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    new-instance v1, Lcom/example/mxextend/ExtendApi$getEndPoint$1;

    invoke-direct {v1, p0, p1}, Lcom/example/mxextend/ExtendApi$getEndPoint$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->getEndPoint(Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 3010
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 3011
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u83b7\u53d6\u7ec8\u70b9========================2"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 3012
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$ZhTIF0MSncd3l_sekFFJEKYD34s;

    invoke-direct {v1, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$ZhTIF0MSncd3l_sekFFJEKYD34s;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_1
    :goto_0
    return-void
.end method

.method public getFavoriteList(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/SearchResultModel;",
            ">;)V"
        }
    .end annotation

    if-nez p1, :cond_0

    .line 2786
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "getFavoriteList:callback= null"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 2789
    :cond_0
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 2790
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 2793
    :cond_1
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$getFavoriteList$1;

    invoke-direct {v1, p0, p1}, Lcom/example/mxextend/ExtendApi$getFavoriteList$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->getFavoriteList(Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 2821
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2822
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "getFavoriteList: exception"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2823
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$GdyGxWGvuY8A9qkBGxbo17D9w64;

    invoke-direct {v1, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$GdyGxWGvuY8A9qkBGxbo17D9w64;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public getHomeOrCompanyData(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/LocationInfo;",
            ">;)V"
        }
    .end annotation

    const-string v0, "callback"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 2611
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    const-string v1, ""

    if-nez v0, :cond_0

    .line 2612
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 2616
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    const/4 v2, 0x0

    if-eqz v0, :cond_1

    invoke-interface {v0, p1}, Lcom/mxnavi/busines/ExtendServiceInterface;->getHomeOrCompanyData(I)Lcom/mxnavi/busines/entity/ResponseData;

    move-result-object p1

    goto :goto_0

    :cond_1
    move-object p1, v2

    :goto_0
    const/4 v0, 0x0

    if-eqz p1, :cond_2

    .line 2617
    invoke-virtual {p1}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v3

    const/16 v4, 0x2710

    if-ne v3, v4, :cond_2

    const/4 v0, 0x1

    :cond_2
    if-eqz v0, :cond_3

    .line 2618
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "getHomeOrCompanyData:callback="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, ".data"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2619
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    .line 2620
    invoke-virtual {p1}, Lcom/mxnavi/busines/entity/ResponseData;->getData()Ljava/lang/String;

    move-result-object p1

    const-class v1, Lcom/example/mxextend/entity/LocationInfo;

    .line 2619
    invoke-virtual {v0, p1, v1}, Lcom/google/gson/Gson;->fromJson(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    const-string v0, "gson.fromJson<LocationIn\u2026ss.java\n                )"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast p1, Lcom/example/mxextend/entity/LocationInfo;

    .line 2623
    check-cast p1, Lcom/example/mxextend/entity/ExtendBaseModel;

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_1

    .line 2625
    :cond_3
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    if-eqz p1, :cond_4

    invoke-virtual {p1}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v2

    :cond_4
    invoke-static {v2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v2}, Ljava/lang/Integer;->intValue()I

    move-result p1

    invoke-direct {v0, p1, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p1

    .line 2628
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2629
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "getHomeOrCompanyData: exception"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2630
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2631
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$LnpTUBLElZpoiLHv0XaUlwGHDiE;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$LnpTUBLElZpoiLHv0XaUlwGHDiE;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_1
    return-void
.end method

.method public getMXnaviAppLoginResult(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/CarltdLoginResultBean;",
            ">;)V"
        }
    .end annotation

    .line 976
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u83b7\u53d6\u767b\u5f55\u4fe1\u606f========================1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 977
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_0

    return-void

    :cond_0
    if-nez p1, :cond_1

    const-string p1, "\u83b7\u53d6\u767b\u5f55\u4fe1\u606f:callback= null"

    .line 981
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 985
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$getMXnaviAppLoginResult$1;

    invoke-direct {v1, p0, p1}, Lcom/example/mxextend/ExtendApi$getMXnaviAppLoginResult$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->getMXnaviAppLoginResult(Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 1012
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "requestAddPass: exception= "

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1013
    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    .line 1014
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$EgMRbCufnICbj_xAHKvD6Vlha80;

    invoke-direct {v1, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$EgMRbCufnICbj_xAHKvD6Vlha80;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public getNaviStage()I
    .locals 4

    .line 2924
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    .line 2925
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "getNaviStage: unbindserivce"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const/4 v0, -0x1

    return v0

    .line 2930
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->getNaviStage()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 2932
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 v0, -0x2

    .line 2935
    :goto_1
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getNaviStage: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0
.end method

.method public getNaviState()I
    .locals 4

    .line 788
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    :cond_0
    const/4 v0, -0x2

    .line 793
    :try_start_0
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v1, :cond_1

    invoke-interface {v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->getNaviState()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v1

    .line 795
    invoke-virtual {v1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 798
    :cond_1
    :goto_0
    iget v1, p0, Lcom/example/mxextend/ExtendApi;->naviStateResultcode:I

    if-eq v0, v1, :cond_2

    .line 799
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getNaviState: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 801
    :cond_2
    iput v0, p0, Lcom/example/mxextend/ExtendApi;->naviStateResultcode:I

    return v0
.end method

.method public final getNaviStateResultcode()I
    .locals 1

    .line 785
    iget v0, p0, Lcom/example/mxextend/ExtendApi;->naviStateResultcode:I

    return v0
.end method

.method public getNaviType()I
    .locals 5

    const/16 v0, 0x24

    .line 856
    :try_start_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_0

    .line 857
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v2, "getNaviType : unbind service"

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 860
    :cond_0
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v1, :cond_1

    invoke-interface {v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->getNaviType()Lcom/mxnavi/busines/entity/ResponseData;

    move-result-object v1

    goto :goto_0

    :cond_1
    const/4 v1, 0x0

    :goto_0
    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    .line 862
    sget-object v2, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    .line 863
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "getNaviType : responseCode = "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v1}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    const-string v4, " response = "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v1}, Lcom/mxnavi/busines/entity/ResponseData;->getData()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    .line 861
    invoke-static {v2, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 865
    invoke-virtual {v1}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v2

    const/16 v3, 0x2710

    if-ne v2, v3, :cond_2

    .line 866
    invoke-virtual {v1}, Lcom/mxnavi/busines/entity/ResponseData;->getData()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-static {v1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    :cond_2
    return v0

    :catch_0
    move-exception v1

    .line 871
    invoke-virtual {v1}, Landroid/os/RemoteException;->printStackTrace()V

    return v0
.end method

.method public getPassPointNum()I
    .locals 4

    .line 2643
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    .line 2648
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->getPassPointNum()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 2650
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 v0, -0x2

    .line 2653
    :goto_0
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getPassPointNum: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0
.end method

.method public final getReconnect()Z
    .locals 1

    .line 387
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->reconnect:Z

    return v0
.end method

.method public getRemainDistance()I
    .locals 4

    .line 1816
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    .line 1819
    :cond_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->getRemainDistance()Lcom/mxnavi/busines/entity/ResponseData;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    .line 1820
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getRemainDistance:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {v3, v0}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1821
    invoke-virtual {v0}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v1

    const/16 v2, 0x2710

    if-ne v1, v2, :cond_2

    .line 1822
    invoke-virtual {v0}, Lcom/mxnavi/busines/entity/ResponseData;->getData()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(Ljava/lang/String;)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "{\n                Intege\u2026tInfo.data)\n            }"

    .line 1821
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Ljava/lang/Number;

    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    move-result v0

    goto :goto_1

    .line 1824
    :cond_2
    invoke-virtual {v0}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    :goto_1
    return v0

    :catch_0
    move-exception v0

    .line 1827
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1828
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "getRemainDistance: exception"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const/4 v0, -0x2

    return v0
.end method

.method public getRemainTime()I
    .locals 4

    .line 1835
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    .line 1838
    :cond_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->getRemainTime()Lcom/mxnavi/busines/entity/ResponseData;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    .line 1839
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getRemainTime:"

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    iget-object v3, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {v3, v0}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1840
    invoke-virtual {v0}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v1

    const/16 v2, 0x2710

    if-ne v1, v2, :cond_2

    .line 1841
    invoke-virtual {v0}, Lcom/mxnavi/busines/entity/ResponseData;->getData()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(Ljava/lang/String;)Ljava/lang/Integer;

    move-result-object v0

    const-string v1, "{\n                Intege\u2026tInfo.data)\n            }"

    .line 1840
    invoke-static {v0, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Ljava/lang/Number;

    invoke-virtual {v0}, Ljava/lang/Number;->intValue()I

    move-result v0

    goto :goto_1

    .line 1843
    :cond_2
    invoke-virtual {v0}, Lcom/mxnavi/busines/entity/ResponseData;->getResponseCode()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    :goto_1
    return v0

    :catch_0
    move-exception v0

    .line 1846
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1847
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "getRemainTime: exception"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const/4 v0, -0x2

    return v0
.end method

.method public getRouteSummaryList(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/RouteSummaryModel;",
            ">;)V"
        }
    .end annotation

    if-nez p1, :cond_0

    .line 2830
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "getRouteSummaryList:callback= null"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 2833
    :cond_0
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 2834
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 2837
    :cond_1
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$getRouteSummaryList$1;

    invoke-direct {v1, p0, p1}, Lcom/example/mxextend/ExtendApi$getRouteSummaryList$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->getRouteSummaryList(Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 2864
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2865
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "getRouteSummaryList: exception"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2866
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$qOwok-Gdf864kUFSf0JN4tHzAHY;

    invoke-direct {v1, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$qOwok-Gdf864kUFSf0JN4tHzAHY;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public getScaleLevel()I
    .locals 4

    .line 1660
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    .line 1665
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->getScaleLevel()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 1667
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 v0, -0x2

    .line 1670
    :goto_1
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getScaleLevel: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0
.end method

.method public getSearchHistoryData(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/SearchResultModel;",
            ">;)V"
        }
    .end annotation

    if-nez p1, :cond_0

    .line 2744
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "getSearchHistoryData:callback= null"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 2747
    :cond_0
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 2748
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 2751
    :cond_1
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$getSearchHistoryData$1;

    invoke-direct {v1, p0, p1}, Lcom/example/mxextend/ExtendApi$getSearchHistoryData$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->getSearchHistory(Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 2777
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2778
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "getSearchHistoryData: exception"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2779
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$mTl5V0SID9lDpc3Mqy7qOQAyEGE;

    invoke-direct {v1, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$mTl5V0SID9lDpc3Mqy7qOQAyEGE;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public getSpeakMode()I
    .locals 4

    .line 1630
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    .line 1635
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->getSpeakMode()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 1637
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 v0, -0x2

    .line 1640
    :goto_0
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "getSpeakMode: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0
.end method

.method public goFavorite()I
    .locals 4

    .line 2894
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    goto :goto_2

    .line 2899
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->goFavorite()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 2901
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 v0, -0x2

    .line 2904
    :goto_1
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "goTeamTrip: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const/4 v0, 0x0

    :goto_2
    return v0
.end method

.method public goHomeOrCompany(IILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    .line 2877
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-interface {v0, p1, p2}, Lcom/mxnavi/busines/ExtendServiceInterface;->goHomeOrCompany(II)I

    move-result p1

    .line 2878
    sget-object p2, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "specialPoiNavi: resultcode="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {p2, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2879
    iget-object p2, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$Tx8gSkeSCCoE873V8Jw5FqrjUhU;

    invoke-direct {v0, p1, p3}, Lcom/example/mxextend/-$$Lambda$ExtendApi$Tx8gSkeSCCoE873V8Jw5FqrjUhU;-><init>(ILcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p2, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 2887
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2888
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "specialPoiNavi: exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2889
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance p2, Lcom/example/mxextend/-$$Lambda$ExtendApi$RUEuj1UTosJ5cZyQf3XH93NnHqM;

    invoke-direct {p2, p3}, Lcom/example/mxextend/-$$Lambda$ExtendApi$RUEuj1UTosJ5cZyQf3XH93NnHqM;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, p2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_0
    return-void
.end method

.method public goSetting(Lcom/example/mxextend/listener/IExtendCallback;)I
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/16 v0, 0x12

    if-nez p1, :cond_0

    .line 1706
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "goSetting:callback= null"

    invoke-static {p1, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 1709
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    .line 1710
    new-instance v1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v2, -0x1

    const-string v3, ""

    invoke-direct {v1, v2, v3}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 1714
    :cond_1
    :try_start_0
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v1, :cond_2

    invoke-interface {v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->goSetting()I

    move-result v1

    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v1

    goto :goto_0

    :cond_2
    const/4 v1, 0x0

    :goto_0
    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v1}, Ljava/lang/Integer;->intValue()I

    move-result v1

    .line 1715
    sget-object v2, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "goSetting: resultcode="

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1716
    iget-object v2, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v3, Lcom/example/mxextend/-$$Lambda$ExtendApi$2-7WakkNQXVaLDYEOVuGrEEMjvI;

    invoke-direct {v3, v1, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$2-7WakkNQXVaLDYEOVuGrEEMjvI;-><init>(ILcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v2, v3}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v1

    .line 1724
    invoke-virtual {v1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1725
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v2, Lcom/example/mxextend/-$$Lambda$ExtendApi$RJTFyQK3vi2jP3mn23eiLFuOMHg;

    invoke-direct {v2, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$RJTFyQK3vi2jP3mn23eiLFuOMHg;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v1, v2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_1
    return v0
.end method

.method public goTeamTrip()I
    .locals 4

    .line 1690
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    .line 1695
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->goTeamTrip()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 1697
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 v0, -0x2

    .line 1700
    :goto_1
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "goTeamTrip: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0
.end method

.method public goToNearbyGasStation(Ljava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 1261
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "goToNearbyGasStation:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 1264
    :cond_0
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "goToNearbyGasStation"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1266
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    .line 1268
    new-instance v1, Lcom/example/mxextend/ExtendApi$goToNearbyGasStation$1;

    invoke-direct {v1, p2}, Lcom/example/mxextend/ExtendApi$goToNearbyGasStation$1;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    .line 1266
    invoke-interface {v0, p1, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->goToNearbyGasStation(Ljava/lang/String;Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 1292
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1293
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "goToNearbyGasStation:exception"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1294
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$AwhGZxBWZ0tcr63oDKbGWVkGfW4;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$AwhGZxBWZ0tcr63oDKbGWVkGfW4;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_1
    :goto_0
    return-void
.end method

.method public isExistInCollect(Ljava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/CollectInfoModel;",
            ">;)V"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 2659
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "isExistInCollect:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 2662
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 2663
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    const-string v1, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 2667
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v1, ""

    invoke-virtual {p1, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p1}, Lcom/mxnavi/busines/ExtendServiceInterface;->isExistInCollect(Ljava/lang/String;)Lcom/mxnavi/busines/entity/ResponseData;

    move-result-object p1

    .line 2668
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "isExistInCollect=="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v2, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {v2, p1}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2669
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$tB8HAbkV1qw8-o8tSeLljkatirE;

    invoke-direct {v1, p1, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$tB8HAbkV1qw8-o8tSeLljkatirE;-><init>(Lcom/mxnavi/busines/entity/ResponseData;Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 2684
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "isExistInCollect: exception"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2685
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2686
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$OKuvV6js9eOkU04qBfNmRqOBV3k;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$OKuvV6js9eOkU04qBfNmRqOBV3k;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_0
    return-void
.end method

.method public isInNavi()I
    .locals 4

    .line 2955
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    goto :goto_2

    .line 2960
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->isInNavi()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 2962
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 v0, -0x2

    .line 2965
    :goto_1
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "bringAppToFront: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :goto_2
    return v0
.end method

.method public isVolumeMute()I
    .locals 4

    .line 2368
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    goto :goto_1

    .line 2371
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->isVolumeMute()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0

    .line 2372
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "isVolumeMute: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 2375
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2376
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "isVolumeMute: exception"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const/4 v0, -0x2

    :goto_1
    return v0
.end method

.method public keywordSearch(Ljava/lang/String;ILcom/example/mxextend/listener/IExtendCallback;)I
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/SearchResultModel;",
            ">;)I"
        }
    .end annotation

    move-object/from16 v0, p3

    const/4 v1, 0x5

    if-nez v0, :cond_0

    .line 1888
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v2, "keywordSearch:callback= null"

    invoke-static {v0, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v1

    .line 1891
    :cond_0
    new-instance v2, Lcom/mxnavi/busines/entity/RequestSearchData;

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/16 v14, 0x3ff

    const/4 v15, 0x0

    move-object v3, v2

    invoke-direct/range {v3 .. v15}, Lcom/mxnavi/busines/entity/RequestSearchData;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IZIZFIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    move-object/from16 v3, p1

    .line 1892
    invoke-virtual {v2, v3}, Lcom/mxnavi/busines/entity/RequestSearchData;->setSearchKey(Ljava/lang/String;)V

    move/from16 v3, p2

    .line 1893
    invoke-virtual {v2, v3}, Lcom/mxnavi/busines/entity/RequestSearchData;->setSearchAction(I)V

    move-object/from16 v3, p0

    .line 1894
    invoke-virtual {v3, v2, v0}, Lcom/example/mxextend/ExtendApi;->searchPoi(Lcom/mxnavi/busines/entity/RequestSearchData;Lcom/example/mxextend/listener/IExtendCallback;)V

    return v1
.end method

.method public keywordSearch(Ljava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)I
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/SearchResultModel;",
            ">;)I"
        }
    .end annotation

    move-object/from16 v0, p2

    const/4 v1, 0x5

    if-nez v0, :cond_0

    .line 1873
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v2, "keywordSearch:callback= null"

    invoke-static {v0, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v1

    .line 1876
    :cond_0
    new-instance v2, Lcom/mxnavi/busines/entity/RequestSearchData;

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/16 v14, 0x3ff

    const/4 v15, 0x0

    move-object v3, v2

    invoke-direct/range {v3 .. v15}, Lcom/mxnavi/busines/entity/RequestSearchData;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IZIZFIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    move-object/from16 v3, p1

    .line 1877
    invoke-virtual {v2, v3}, Lcom/mxnavi/busines/entity/RequestSearchData;->setSearchKey(Ljava/lang/String;)V

    move-object/from16 v3, p0

    .line 1878
    invoke-virtual {v3, v2, v0}, Lcom/example/mxextend/ExtendApi;->searchPoi(Lcom/mxnavi/busines/entity/RequestSearchData;Lcom/example/mxextend/listener/IExtendCallback;)V

    return v1
.end method

.method public lightFigureSwitch(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 3024
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "setDayNightStyle:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 3025
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    const-string v1, ""

    if-nez v0, :cond_1

    .line 3026
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    goto :goto_0

    .line 3029
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v2, Lcom/example/mxextend/ExtendApi$lightFigureSwitch$1;

    invoke-direct {v2, p2}, Lcom/example/mxextend/ExtendApi$lightFigureSwitch$1;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v2, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, p1, v2}, Lcom/mxnavi/busines/ExtendServiceInterface;->lightFigureSwitch(ILcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 3040
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 3041
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x2

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    .line 3042
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "setDayNightStyle:exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :cond_2
    :goto_0
    return-void
.end method

.method public lookOverView()I
    .locals 4

    .line 1675
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 v0, -0x1

    return v0

    .line 1680
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->lookOverView()I

    move-result v0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0}, Ljava/lang/Integer;->intValue()I

    move-result v0
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception v0

    .line 1682
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 v0, -0x2

    .line 1685
    :goto_1
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "lookOverView: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0
.end method

.method public mapOpera(IILcom/example/mxextend/listener/IExtendCallback;)I
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/4 v0, 0x1

    if-nez p3, :cond_0

    .line 1375
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "mapOpera:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 1379
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    const-string v2, ""

    if-nez v1, :cond_1

    .line 1380
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 p2, -0x1

    invoke-direct {p1, p2, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 1385
    :cond_1
    :try_start_0
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v3, "mapOpera:=============mapOpera=======================3.6.5"

    invoke-static {v1, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1386
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v1, :cond_2

    .line 1389
    new-instance v3, Lcom/example/mxextend/ExtendApi$mapOpera$1;

    invoke-direct {v3, p3}, Lcom/example/mxextend/ExtendApi$mapOpera$1;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v3, Lcom/mxnavi/busines/IServiceCallBack;

    .line 1386
    invoke-interface {v1, p1, p2, v3}, Lcom/mxnavi/busines/ExtendServiceInterface;->mapOpera(IILcom/mxnavi/busines/IServiceCallBack;)I
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 1400
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1401
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 p2, -0x2

    invoke-direct {p1, p2, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :cond_2
    :goto_0
    return v0
.end method

.method public naviOpera(IILcom/example/mxextend/listener/IExtendCallback;)I
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/16 v0, 0xd

    if-nez p3, :cond_0

    .line 2025
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "naviOpera:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 2028
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    const-string v2, ""

    if-nez v1, :cond_1

    .line 2029
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 p2, -0x1

    invoke-direct {p1, p2, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 2034
    :cond_1
    :try_start_0
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v1, :cond_2

    new-instance v3, Lcom/example/mxextend/ExtendApi$naviOpera$1;

    invoke-direct {v3, p3}, Lcom/example/mxextend/ExtendApi$naviOpera$1;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v3, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v1, p1, p2, v3}, Lcom/mxnavi/busines/ExtendServiceInterface;->naviOpera(IILcom/mxnavi/busines/IServiceCallBack;)I
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 2046
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2047
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "naviOpera: exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2048
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 p2, -0x2

    invoke-direct {p1, p2, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :cond_2
    :goto_0
    return v0
.end method

.method public naviToPoi(ZLcom/example/mxextend/entity/LocationInfo;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/example/mxextend/entity/LocationInfo;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    .line 1250
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "================naviToPoinaviToPoi=============1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    if-eqz p2, :cond_1

    .line 1251
    invoke-virtual {p2}, Lcom/example/mxextend/entity/LocationInfo;->getLatitude()D

    move-result-wide v1

    const-wide/16 v3, 0x0

    cmpg-double v1, v1, v3

    if-lez v1, :cond_1

    invoke-virtual {p2}, Lcom/example/mxextend/entity/LocationInfo;->getLongitude()D

    move-result-wide v1

    cmpg-double v1, v1, v3

    if-gtz v1, :cond_0

    goto :goto_0

    .line 1255
    :cond_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "naviToPoi: info =="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1256
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {v0, p2}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    invoke-virtual {p0, p1, p2, p3}, Lcom/example/mxextend/ExtendApi;->naviToPoi(ZLjava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void

    .line 1252
    :cond_1
    :goto_0
    invoke-static {p3}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/16 p2, -0x2713

    const-string v0, "\u670d\u52a1\u672a\u7ed1\u5b9a"

    invoke-direct {p1, p2, v0}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void
.end method

.method public naviToPoi(ZLjava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    .line 1154
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "================naviToPoi=============1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    if-nez p3, :cond_0

    const-string p1, "naviToPoi:callback= null"

    .line 1156
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 1159
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 1160
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 p2, -0x1

    const-string v0, "\u670d\u52a1\u672a\u7ed1\u5b9a"

    invoke-direct {p1, p2, v0}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 1164
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    .line 1167
    new-instance v1, Lcom/example/mxextend/ExtendApi$naviToPoi$1;

    invoke-direct {v1, p0, p3}, Lcom/example/mxextend/ExtendApi$naviToPoi$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    .line 1164
    invoke-interface {v0, p1, p2, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->naviToPoi(ZLjava/lang/String;Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 1184
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1185
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "naviToPoi: exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1186
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance p2, Lcom/example/mxextend/-$$Lambda$ExtendApi$p9LANwqGTzkjE0NGkwCKvIFySB8;

    invoke-direct {p2, p3}, Lcom/example/mxextend/-$$Lambda$ExtendApi$p9LANwqGTzkjE0NGkwCKvIFySB8;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, p2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_0
    return-void
.end method

.method public navigation(Lcom/example/mxextend/entity/LocationInfo;ZLcom/example/mxextend/listener/IExtendCallback;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/entity/LocationInfo;",
            "Z",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    .line 1202
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "===============navigation====================1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "\u670d\u52a1\u672a\u7ed1\u5b9a"

    if-eqz p1, :cond_4

    .line 1203
    invoke-virtual {p1}, Lcom/example/mxextend/entity/LocationInfo;->getLatitude()D

    move-result-wide v2

    const-wide/16 v4, 0x0

    cmpg-double v2, v2, v4

    if-lez v2, :cond_4

    invoke-virtual {p1}, Lcom/example/mxextend/entity/LocationInfo;->getLongitude()D

    move-result-wide v2

    cmpg-double v2, v2, v4

    if-gtz v2, :cond_0

    goto :goto_1

    :cond_0
    if-nez p3, :cond_1

    const-string p1, "naviToPoi:callback= null"

    .line 1208
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 1211
    :cond_1
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_2

    .line 1212
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 p2, -0x1

    invoke-direct {p1, p2, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 1216
    :cond_2
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_3

    .line 1217
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {v1, p1}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    .line 1219
    new-instance v1, Lcom/example/mxextend/ExtendApi$navigation$1;

    invoke-direct {v1, p0, p3}, Lcom/example/mxextend/ExtendApi$navigation$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    .line 1216
    invoke-interface {v0, p1, p2, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->navigation(Ljava/lang/String;ZLcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 1236
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1237
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "naviToPoi: exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1238
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance p2, Lcom/example/mxextend/-$$Lambda$ExtendApi$x-vDT2SCrmGGfcMz-Tqudz01r5c;

    invoke-direct {p2, p3}, Lcom/example/mxextend/-$$Lambda$ExtendApi$x-vDT2SCrmGGfcMz-Tqudz01r5c;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, p2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_3
    :goto_0
    return-void

    :cond_4
    :goto_1
    if-eqz p3, :cond_5

    .line 1204
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/16 p2, -0x2713

    invoke-direct {p1, p2, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :cond_5
    return-void
.end method

.method public navigationPlanningRoute(Lcom/example/mxextend/entity/LocationDetailModel;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/entity/LocationDetailModel;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/LocationDetail;",
            ">;)V"
        }
    .end annotation

    .line 522
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u9884\u7ea6\u5bfc\u822a \u5bfc\u822a\u89c4\u5212\u8def\u7ebf========================1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    if-nez p2, :cond_0

    const-string p1, "\u9884\u7ea6\u5bfc\u822a \u5bfc\u822a\u89c4\u5212\u8def\u7ebf:callback= null"

    .line 524
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 527
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    const-string p1, "======================\u9884\u7ea6\u5bfc\u822a \u5bfc\u822a\u89c4\u5212\u8def\u7ebf \u670d\u52a1\u672a\u542f\u52a8========================1"

    .line 528
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 533
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    const-string v1, "\u9884\u7ea6\u5bfc\u822a \u5bfc\u822a\u89c4\u5212\u8def\u7ebf \u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 532
    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    :cond_1
    :try_start_0
    const-string v1, "======================\u9884\u7ea6\u5bfc\u822a \u5bfc\u822a\u89c4\u5212\u8def\u7ebf========================1"

    .line 541
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 542
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {v1, p1}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    const-string v1, "gson.toJson(info)"

    invoke-static {p1, v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V

    .line 543
    move-object v1, p1

    check-cast v1, Ljava/lang/CharSequence;

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-nez v1, :cond_2

    .line 544
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    .line 546
    new-instance v1, Lcom/example/mxextend/ExtendApi$navigationPlanningRoute$1;

    invoke-direct {v1, p0, p2}, Lcom/example/mxextend/ExtendApi$navigationPlanningRoute$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    .line 544
    invoke-interface {v0, p1, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->navigationPlanningRoute(Ljava/lang/String;Lcom/mxnavi/busines/IServiceCallBack;)V

    goto :goto_0

    :cond_2
    const-string p1, "======================\u9884\u7ea6\u5bfc\u822a \u5bfc\u822a\u89c4\u5212\u8def\u7ebf========================null"

    .line 568
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 574
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 575
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "======================\u9884\u7ea6\u5bfc\u822a \u5bfc\u822a\u89c4\u5212\u8def\u7ebf========================2"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 576
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$2aVXog2AHb6iLiDob_tJ9xh2P3U;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$2aVXog2AHb6iLiDob_tJ9xh2P3U;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_0
    return-void
.end method

.method public pageOprea(IILcom/example/mxextend/listener/IExtendCallback;)I
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/16 v0, 0x13

    if-nez p3, :cond_0

    .line 2286
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "pageOprea:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    :cond_0
    const-string v1, ""

    if-nez p1, :cond_1

    .line 2290
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/16 p2, -0x2713

    invoke-direct {p1, p2, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 2293
    :cond_1
    iget-boolean v2, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v2, :cond_2

    .line 2294
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 p2, -0x1

    invoke-direct {p1, p2, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 2298
    :cond_2
    :try_start_0
    new-instance v1, Lcom/mxnavi/busines/entity/PageOpreaData;

    invoke-direct {v1}, Lcom/mxnavi/busines/entity/PageOpreaData;-><init>()V

    .line 2299
    invoke-virtual {v1, p1}, Lcom/mxnavi/busines/entity/PageOpreaData;->setPageId(I)V

    .line 2300
    invoke-virtual {v1, p2}, Lcom/mxnavi/busines/entity/PageOpreaData;->setAction(I)V

    .line 2302
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz p1, :cond_3

    new-instance p2, Lcom/example/mxextend/ExtendApi$pageOprea$responseCode$2;

    invoke-direct {p2, p0, p3}, Lcom/example/mxextend/ExtendApi$pageOprea$responseCode$2;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast p2, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {p1, v1, p2}, Lcom/mxnavi/busines/ExtendServiceInterface;->pageOpera(Lcom/mxnavi/busines/entity/PageOpreaData;Lcom/mxnavi/busines/IServiceCallBack;)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_3
    const/4 p1, 0x0

    .line 2319
    :goto_0
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 2320
    sget-object p2, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "pageOprea: resultcode="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {p2, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2321
    iget-object p2, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$v9G6eqSjajYD-o4KeL1DDApjk8s;

    invoke-direct {v1, p1, p3}, Lcom/example/mxextend/-$$Lambda$ExtendApi$v9G6eqSjajYD-o4KeL1DDApjk8s;-><init>(ILcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p2, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p1

    .line 2329
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2330
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "pageOprea: exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2331
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance p2, Lcom/example/mxextend/-$$Lambda$ExtendApi$iz9IoCIt4z-vI9l8tNqsQSRS7y8;

    invoke-direct {p2, p3}, Lcom/example/mxextend/-$$Lambda$ExtendApi$iz9IoCIt4z-vI9l8tNqsQSRS7y8;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, p2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_1
    return v0
.end method

.method public pageOprea(Lcom/mxnavi/busines/entity/PageOpreaData;Lcom/example/mxextend/listener/IExtendCallback;)I
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/mxnavi/busines/entity/PageOpreaData;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    .line 2235
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "pageOprea:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const/16 v1, 0x13

    if-nez p2, :cond_0

    const-string p1, "pageOprea:callback= null"

    .line 2237
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v1

    :cond_0
    const-string v2, ""

    if-eqz p1, :cond_4

    .line 2240
    invoke-virtual {p1}, Lcom/mxnavi/busines/entity/PageOpreaData;->getPageId()I

    move-result v3

    if-nez v3, :cond_1

    goto :goto_2

    .line 2244
    :cond_1
    iget-boolean v3, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v3, :cond_2

    .line 2245
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    invoke-direct {p1, v0, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v1

    .line 2250
    :cond_2
    :try_start_0
    iget-object v2, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v2, :cond_3

    new-instance v3, Lcom/example/mxextend/ExtendApi$pageOprea$responseCode$1;

    invoke-direct {v3, p0, p2}, Lcom/example/mxextend/ExtendApi$pageOprea$responseCode$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v3, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v2, p1, v3}, Lcom/mxnavi/busines/ExtendServiceInterface;->pageOpera(Lcom/mxnavi/busines/entity/PageOpreaData;Lcom/mxnavi/busines/IServiceCallBack;)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_3
    const/4 p1, 0x0

    .line 2268
    :goto_0
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 2269
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "pageOprea: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p1

    .line 2271
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2272
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "pageOprea: exception"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2273
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$72mGeetv74cM_B2p4JsUs4ole-E;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$72mGeetv74cM_B2p4JsUs4ole-E;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_1
    return v1

    .line 2241
    :cond_4
    :goto_2
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/16 v0, -0x2713

    invoke-direct {p1, v0, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v1
.end method

.method public removeAccountChangedListener(Lcom/example/mxextend/listener/IAccountListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 1492
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->accountListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public removeExtendListener(Lcom/example/mxextend/listener/IExtendListener;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    .line 889
    :cond_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/CopyOnWriteArrayList;->remove(Ljava/lang/Object;)Z

    return-void
.end method

.method public removeHomeOrCompanyChangedListener(Lcom/example/mxextend/listener/IHomeOrCompanyListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2919
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->homeOrCompanyListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public removeLocationChangedListener(Lcom/example/mxextend/listener/ILocationChangedListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2504
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->locationListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public removeLocationUpdateCallBack(Lcom/example/mxextend/listener/ILocationUpdateCallBack;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2574
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->locationUpdateCallBacks:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public removeManualVoiceInteractionListener(Lcom/example/mxextend/listener/IManualVoiceInteractionListener;)V
    .locals 2

    if-eqz p1, :cond_0

    .line 3222
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->manualVoiceInteractionListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    .line 3223
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->manualVoiceInteractionListeners:Ljava/util/LinkedList;

    if-eqz p1, :cond_0

    .line 3225
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    .line 3226
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "==========\u79fb\u9664\u624b\u52a8\u8bed\u97f3\u4ea4\u4e92\u76d1\u542c========"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->manualVoiceInteractionListeners:Ljava/util/LinkedList;

    invoke-virtual {v1}, Ljava/util/LinkedList;->size()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 3224
    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :cond_0
    return-void
.end method

.method public removePageChangedListener(Lcom/example/mxextend/listener/IPageChangedListener;)V
    .locals 2

    if-eqz p1, :cond_0

    .line 2594
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->pageChangedListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    .line 2596
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    .line 2597
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "==========\u79fb\u9664\u9875\u9762\u5207\u6362\u76d1\u542c========"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->pageChangedListeners:Ljava/util/LinkedList;

    invoke-virtual {v1}, Ljava/util/LinkedList;->size()I

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    .line 2595
    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :cond_0
    return-void
.end method

.method public removeRouteDataListener(Lcom/example/mxextend/listener/IRouteDataListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2518
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->routeDataListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public removeSearchDataListener(Lcom/example/mxextend/listener/ISearchDataListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2532
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->searchDataListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public removeSearchResultListener(Lcom/example/mxextend/listener/ISearchResultListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2546
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->searchResultListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public removeServiceConnectedListener(Lcom/example/mxextend/listener/IServiceConnectedListener;)V
    .locals 1

    if-nez p1, :cond_0

    return-void

    .line 265
    :cond_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnectedListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {v0, p1}, Ljava/util/concurrent/CopyOnWriteArrayList;->contains(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    .line 266
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnectedListeners:Ljava/util/concurrent/CopyOnWriteArrayList;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/CopyOnWriteArrayList;->remove(Ljava/lang/Object;)Z

    :cond_1
    return-void
.end method

.method public removeSuggestDataListener(Lcom/example/mxextend/listener/ISuggestionResultListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2560
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->suggestionResultListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public removeWeatherInfoListenerListener(Lcom/example/mxextend/listener/IWeatherInfoListener;)V
    .locals 1

    if-eqz p1, :cond_0

    .line 2950
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->weatherInfoListeners:Ljava/util/LinkedList;

    invoke-virtual {v0, p1}, Ljava/util/LinkedList;->remove(Ljava/lang/Object;)Z

    :cond_0
    return-void
.end method

.method public requestAddPass(Lcom/mxnavi/busines/entity/ModifyNaviViaModel;Lcom/example/mxextend/listener/IExtendCallback;)I
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/mxnavi/busines/entity/ModifyNaviViaModel;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/16 v0, 0xa

    if-nez p2, :cond_0

    .line 2423
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "requestAddPass:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 2426
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    .line 2427
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 2431
    :cond_1
    :try_start_0
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v1, :cond_2

    new-instance v2, Lcom/example/mxextend/ExtendApi$requestAddPass$1;

    invoke-direct {v2, p0, p2}, Lcom/example/mxextend/ExtendApi$requestAddPass$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v2, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v1, p1, v2}, Lcom/mxnavi/busines/ExtendServiceInterface;->requestAddPass(Lcom/mxnavi/busines/entity/ModifyNaviViaModel;Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 2445
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2446
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "requestAddPass: exception="

    invoke-static {p1, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2447
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$rHBdx2uKBzM6LRur2yFxZUTKRiY;

    invoke-direct {v1, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$rHBdx2uKBzM6LRur2yFxZUTKRiY;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return v0
.end method

.method public requestAlongRouteData(Ljava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/SearchResultModel;",
            ">;)V"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 1970
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "requestAlongRouteData:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 1975
    :cond_0
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 1976
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    const-string v1, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 1979
    :cond_1
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    const/4 v1, 0x0

    new-instance v2, Lcom/example/mxextend/ExtendApi$requestAlongRouteData$1;

    invoke-direct {v2, p0, p2}, Lcom/example/mxextend/ExtendApi$requestAlongRouteData$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v2, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, p1, v1, v2}, Lcom/mxnavi/busines/ExtendServiceInterface;->searchAlongRoute(Ljava/lang/String;ZLcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 2001
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2002
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "requestAlongRouteData: exception"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2003
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$M6oHhz7rOm-FWq2lwnBmU2qwSa0;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$M6oHhz7rOm-FWq2lwnBmU2qwSa0;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public requestGuideInfo(Lcom/mxnavi/busines/entity/RequestGuideInfoModel;Lcom/example/mxextend/listener/IExtendCallback;)I
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/mxnavi/busines/entity/RequestGuideInfoModel;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/GuideInfoModel;",
            ">;)I"
        }
    .end annotation

    const/16 p1, 0xf

    if-nez p2, :cond_0

    .line 2459
    sget-object p2, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "requestGuideInfo:callback= null"

    invoke-static {p2, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return p1

    .line 2462
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 2463
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return p1

    .line 2467
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$requestGuideInfo$1;

    invoke-direct {v1, p0, p2}, Lcom/example/mxextend/ExtendApi$requestGuideInfo$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->requestGuideInfo(Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 2485
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2486
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$Aqv5lO8L4oGRWYRpRdmd5RGjF1A;

    invoke-direct {v1, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$Aqv5lO8L4oGRWYRpRdmd5RGjF1A;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return p1
.end method

.method public requestPoiData(Ljava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 15
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/SearchResultModel;",
            ">;)V"
        }
    .end annotation

    move-object/from16 v0, p2

    .line 1309
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v2, "=========\u5bfc\u822a\u53bb\u67d0\u5730==============1.0.31"

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    if-nez v0, :cond_0

    const-string v0, "requestPoiData:callback= null"

    .line 1311
    invoke-static {v1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 1314
    :cond_0
    new-instance v1, Lcom/mxnavi/busines/entity/RequestSearchData;

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/16 v13, 0x3ff

    const/4 v14, 0x0

    move-object v2, v1

    invoke-direct/range {v2 .. v14}, Lcom/mxnavi/busines/entity/RequestSearchData;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IZIZFIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    move-object/from16 v2, p1

    .line 1315
    invoke-virtual {v1, v2}, Lcom/mxnavi/busines/entity/RequestSearchData;->setSearchKey(Ljava/lang/String;)V

    move-object v2, p0

    .line 1316
    invoke-virtual {p0, v1, v0}, Lcom/example/mxextend/ExtendApi;->searchPoi(Lcom/mxnavi/busines/entity/RequestSearchData;Lcom/example/mxextend/listener/IExtendCallback;)V

    return-void
.end method

.method public requestRouteEx(Lcom/mxnavi/busines/entity/RequestRouteExModel;Lcom/example/mxextend/listener/IExtendCallback;)I
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/mxnavi/busines/entity/RequestRouteExModel;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/16 v0, 0x9

    if-nez p2, :cond_0

    .line 2386
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "requestRouteEx:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 2389
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    .line 2390
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 2395
    :cond_1
    :try_start_0
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v1, :cond_2

    new-instance v2, Lcom/example/mxextend/ExtendApi$requestRouteEx$1;

    invoke-direct {v2, p0, p2}, Lcom/example/mxextend/ExtendApi$requestRouteEx$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v2, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v1, p1, v2}, Lcom/mxnavi/busines/ExtendServiceInterface;->requestRouteEx(Lcom/mxnavi/busines/entity/RequestRouteExModel;Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 2409
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2410
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "requestRouteEx: exception="

    invoke-static {p1, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2411
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$1a22TnSv_aaqzsSw7lDkAgfObN0;

    invoke-direct {v1, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$1a22TnSv_aaqzsSw7lDkAgfObN0;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return v0
.end method

.method public restoreNavigation(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/HistoricaRouteInfo;",
            ">;)V"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 3129
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "restoreNavigation:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 3130
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    const-string v1, ""

    if-nez v0, :cond_1

    .line 3131
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    goto :goto_0

    .line 3134
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v2, Lcom/example/mxextend/ExtendApi$restoreNavigation$1;

    invoke-direct {v2, p2}, Lcom/example/mxextend/ExtendApi$restoreNavigation$1;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v2, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, p1, v2}, Lcom/mxnavi/busines/ExtendServiceInterface;->restoreNavigation(ILcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 3150
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 3151
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x2

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    .line 3152
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "restoreNavigation:exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :cond_2
    :goto_0
    return-void
.end method

.method public searchAlongRoute(Ljava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/SearchResultModel;",
            ">;)V"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 1925
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "searchAlongRoute:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 1929
    :cond_0
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 1930
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    const-string v1, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 1934
    :cond_1
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    const/4 v1, 0x1

    new-instance v2, Lcom/example/mxextend/ExtendApi$searchAlongRoute$1;

    invoke-direct {v2, p0, p2}, Lcom/example/mxextend/ExtendApi$searchAlongRoute$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v2, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, p1, v1, v2}, Lcom/mxnavi/busines/ExtendServiceInterface;->searchAlongRoute(Ljava/lang/String;ZLcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 1957
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1958
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "searchAlongRoute: exception"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1959
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$pquT3opnDkalQlY96exjcRJrdzg;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$pquT3opnDkalQlY96exjcRJrdzg;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public searchNearByPoi(Ljava/lang/String;Ljava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)I
    .locals 16
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            "Ljava/lang/String;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/SearchResultModel;",
            ">;)I"
        }
    .end annotation

    move-object/from16 v0, p3

    const/4 v1, 0x7

    if-nez v0, :cond_0

    .line 1858
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v2, "searchNearByPoi:callback= null"

    invoke-static {v0, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v1

    .line 1861
    :cond_0
    new-instance v2, Lcom/mxnavi/busines/entity/RequestSearchData;

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x0

    const/4 v10, 0x0

    const/4 v11, 0x0

    const/4 v12, 0x0

    const/4 v13, 0x0

    const/16 v14, 0x3ff

    const/4 v15, 0x0

    move-object v3, v2

    invoke-direct/range {v3 .. v15}, Lcom/mxnavi/busines/entity/RequestSearchData;-><init>(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IZIZFIIILkotlin/jvm/internal/DefaultConstructorMarker;)V

    move-object/from16 v3, p1

    .line 1862
    invoke-virtual {v2, v3}, Lcom/mxnavi/busines/entity/RequestSearchData;->setNearbyKey(Ljava/lang/String;)V

    move-object/from16 v3, p2

    .line 1863
    invoke-virtual {v2, v3}, Lcom/mxnavi/busines/entity/RequestSearchData;->setSearchKey(Ljava/lang/String;)V

    move-object/from16 v3, p0

    .line 1864
    invoke-virtual {v3, v2, v0}, Lcom/example/mxextend/ExtendApi;->searchPoi(Lcom/mxnavi/busines/entity/RequestSearchData;Lcom/example/mxextend/listener/IExtendCallback;)V

    return v1
.end method

.method public searchPoi(Lcom/mxnavi/busines/entity/RequestSearchData;Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/mxnavi/busines/entity/RequestSearchData;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/SearchResultModel;",
            ">;)V"
        }
    .end annotation

    .line 1029
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "==================searchNearByPoi================1.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    if-nez p2, :cond_0

    const-string p1, "searchPoi:callback= null"

    .line 1031
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 1035
    :cond_0
    :try_start_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 1037
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    const-string v1, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1036
    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    :cond_1
    const/16 v0, -0x2713

    if-nez p1, :cond_2

    .line 1046
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const-string v1, "\u53c2\u6570\u6709\u8bef\uff0cRequestSearchData \u4e0d\u80fd\u4e3a null"

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1045
    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 1053
    :cond_2
    invoke-virtual {p1}, Lcom/mxnavi/busines/entity/RequestSearchData;->getSearchKey()Ljava/lang/String;

    move-result-object v1

    check-cast v1, Ljava/lang/CharSequence;

    invoke-static {v1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v1

    if-eqz v1, :cond_3

    .line 1055
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const-string v1, "\u53c2\u6570\u6709\u8bef\uff0c\u68c0\u7d22\u5173\u952e\u5b57\u4e0d\u80fd\u4e3a\u7a7a"

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 1054
    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    .line 1062
    :cond_3
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_4

    invoke-virtual {p1}, Lcom/mxnavi/busines/entity/RequestSearchData;->getNearbyKey()Ljava/lang/String;

    move-result-object v1

    .line 1063
    invoke-virtual {p1}, Lcom/mxnavi/busines/entity/RequestSearchData;->getSearchKey()Ljava/lang/String;

    move-result-object v2

    .line 1064
    invoke-virtual {p1}, Lcom/mxnavi/busines/entity/RequestSearchData;->isNeedShow()Z

    move-result v3

    .line 1065
    invoke-virtual {p1}, Lcom/mxnavi/busines/entity/RequestSearchData;->getIntentionType()I

    move-result v4

    .line 1066
    invoke-virtual {p1}, Lcom/mxnavi/busines/entity/RequestSearchData;->getViaIndex()I

    move-result v5

    .line 1067
    new-instance p1, Lcom/example/mxextend/ExtendApi$searchPoi$1;

    invoke-direct {p1, p0, p2}, Lcom/example/mxextend/ExtendApi$searchPoi$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    move-object v6, p1

    check-cast v6, Lcom/mxnavi/busines/IServiceCallBack;

    .line 1062
    invoke-interface/range {v0 .. v6}, Lcom/mxnavi/busines/ExtendServiceInterface;->searchNearByPoi(Ljava/lang/String;Ljava/lang/String;ZIILcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 1106
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1107
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "searchNearByPoi: exception"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1108
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$3MEdjZC5oqPrbHErr3OExHDz-iU;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$3MEdjZC5oqPrbHErr3OExHDz-iU;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_4
    :goto_0
    return-void
.end method

.method public selectRoute(ILcom/example/mxextend/listener/IExtendCallback;)I
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const-string v0, "callback"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 2172
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    const/16 v1, 0xc

    const-string v2, ""

    if-nez v0, :cond_0

    .line 2173
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    invoke-direct {p1, v0, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v1

    .line 2177
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    new-instance v3, Lcom/example/mxextend/ExtendApi$selectRoute$result$1;

    invoke-direct {v3, p0, p2}, Lcom/example/mxextend/ExtendApi$selectRoute$result$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v3, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, p1, v3}, Lcom/mxnavi/busines/ExtendServiceInterface;->selectRouteToNavi(ILcom/mxnavi/busines/IServiceCallBack;)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    .line 2191
    :goto_0
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "selectRoute: resultcode="

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v0, v3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const/16 v0, 0x2710

    if-nez p1, :cond_2

    goto :goto_1

    .line 2192
    :cond_2
    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result v3

    if-ne v3, v0, :cond_3

    .line 2193
    new-instance p1, Lcom/example/mxextend/entity/ExtendBaseModel;

    invoke-direct {p1}, Lcom/example/mxextend/entity/ExtendBaseModel;-><init>()V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_2

    .line 2195
    :cond_3
    :goto_1
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    invoke-direct {v0, p1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_2

    :catch_0
    move-exception p1

    .line 2198
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2199
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "selectRoute: exception"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2200
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x2

    invoke-direct {p1, v0, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :goto_2
    return v1
.end method

.method public sendWxPosition(DD)I
    .locals 8

    .line 2071
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 p1, -0x1

    return p1

    .line 2075
    :cond_0
    :try_start_0
    new-instance v7, Lcom/example/mxextend/entity/PoiBean;

    const-string v1, ""

    const-string v2, ""

    move-object v0, v7

    move-wide v3, p1

    move-wide v5, p3

    invoke-direct/range {v0 .. v6}, Lcom/example/mxextend/entity/PoiBean;-><init>(Ljava/lang/String;Ljava/lang/String;DD)V

    .line 2076
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz p1, :cond_1

    iget-object p2, p0, Lcom/example/mxextend/ExtendApi;->gson:Lcom/google/gson/Gson;

    invoke-virtual {p2, v7}, Lcom/google/gson/Gson;->toJson(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p2

    invoke-interface {p1, p2}, Lcom/mxnavi/busines/ExtendServiceInterface;->sendWxPosition(Ljava/lang/String;)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_1
    const/4 p1, 0x0

    :goto_0
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p1

    .line 2078
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 p1, -0x2

    .line 2081
    :goto_1
    sget-object p2, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance p3, Ljava/lang/StringBuilder;

    invoke-direct {p3}, Ljava/lang/StringBuilder;-><init>()V

    const-string p4, "sendWxPosition:responseCode="

    invoke-virtual {p3, p4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p3

    invoke-virtual {p3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p3

    invoke-static {p2, p3}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return p1
.end method

.method public final setCacheExtendCallBack(Lcom/example/mxextend/listener/IExtendJsonCallback;)V
    .locals 0

    .line 389
    iput-object p1, p0, Lcom/example/mxextend/ExtendApi;->cacheExtendCallBack:Lcom/example/mxextend/listener/IExtendJsonCallback;

    return-void
.end method

.method public final setCacheProtocol(Ljava/lang/String;)V
    .locals 0

    .line 388
    iput-object p1, p0, Lcom/example/mxextend/ExtendApi;->cacheProtocol:Ljava/lang/String;

    return-void
.end method

.method public setCompanyAddressResult(ZLcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/HomeCompanyItemBean;",
            ">;)V"
        }
    .end annotation

    const-string v0, "callback"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 473
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u8bbe\u7f6e\u516c\u53f8========================"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 474
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "======================isbind========================"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-boolean v2, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 475
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    return-void

    .line 479
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    .line 481
    new-instance v1, Lcom/example/mxextend/ExtendApi$setCompanyAddressResult$1;

    invoke-direct {v1, p0, p2}, Lcom/example/mxextend/ExtendApi$setCompanyAddressResult$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    .line 479
    invoke-interface {v0, p1, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->setCompanyAddressResult(ZLcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 505
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 506
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "requestAddPass: exception="

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 507
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$_wDEZkqejzE2JGYnaZbl9CqMcTs;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$_wDEZkqejzE2JGYnaZbl9CqMcTs;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_0
    return-void
.end method

.method public setDayNightStyle(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 2722
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "setDayNightStyle:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_1

    .line 2723
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    const-string v1, ""

    if-nez v0, :cond_1

    .line 2724
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    goto :goto_1

    .line 2727
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    invoke-interface {v0, p1}, Lcom/mxnavi/busines/ExtendServiceInterface;->setDayNightStyle(I)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    :goto_0
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 2728
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "setDayNightStyle: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v0, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const/16 v0, 0x2710

    if-ne p1, v0, :cond_3

    .line 2730
    new-instance p1, Lcom/example/mxextend/entity/ExtendBaseModel;

    invoke-direct {p1}, Lcom/example/mxextend/entity/ExtendBaseModel;-><init>()V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->success(Lcom/example/mxextend/entity/ExtendBaseModel;)V

    goto :goto_1

    .line 2732
    :cond_3
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    invoke-direct {v0, p1, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p1

    .line 2735
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2736
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x2

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    .line 2737
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "setDayNightStyle:exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :goto_1
    return-void
.end method

.method public setHomeAddressResult(ZLcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/HomeCompanyItemBean;",
            ">;)V"
        }
    .end annotation

    const-string v0, "callback"

    invoke-static {p2, v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V

    .line 424
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u8bbe\u7f6e\u5bb6========================"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 425
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "======================isbind========================"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-boolean v2, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 426
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    return-void

    .line 430
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    .line 432
    new-instance v1, Lcom/example/mxextend/ExtendApi$setHomeAddressResult$1;

    invoke-direct {v1, p0, p2}, Lcom/example/mxextend/ExtendApi$setHomeAddressResult$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    .line 430
    invoke-interface {v0, p1, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->setHomeAddressResult(ZLcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 456
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 457
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "requestAddPass: exception="

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 458
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v0, Lcom/example/mxextend/-$$Lambda$ExtendApi$cno7kFLHMp3ZLMv0LOUj_aNfhsE;

    invoke-direct {v0, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$cno7kFLHMp3ZLMv0LOUj_aNfhsE;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v0}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_0
    return-void
.end method

.method public setNaviScreen(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    .line 2693
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======\u62c9\u8d77\u5bfc\u822a\u5230\u524d\u53f0===="

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    if-nez p1, :cond_0

    const-string p1, "setNaviScreen:callback= null"

    .line 2695
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 2696
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_1

    .line 2697
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    goto :goto_0

    .line 2700
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$setNaviScreen$1;

    invoke-direct {v1, p1}, Lcom/example/mxextend/ExtendApi$setNaviScreen$1;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->setNaviScreen(Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 2712
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2713
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v2, "setNaviScreen: exception"

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2714
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2715
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$daBXJiV0edpoD8nQo-scEWiHQms;

    invoke-direct {v1, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$daBXJiV0edpoD8nQo-scEWiHQms;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public final setNaviStateResultcode(I)V
    .locals 0

    .line 785
    iput p1, p0, Lcom/example/mxextend/ExtendApi;->naviStateResultcode:I

    return-void
.end method

.method public final setReconnect(Z)V
    .locals 0

    .line 387
    iput-boolean p1, p0, Lcom/example/mxextend/ExtendApi;->reconnect:Z

    return-void
.end method

.method public setSpeakMode(I)I
    .locals 3

    .line 1645
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    const/4 p1, -0x1

    return p1

    .line 1650
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v0}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-interface {v0, p1}, Lcom/mxnavi/busines/ExtendServiceInterface;->setSpeakMode(I)I

    move-result p1
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 1652
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    const/4 p1, -0x2

    .line 1655
    :goto_0
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "setSpeakMode: resultcode="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return p1
.end method

.method public setSpecialPoi(ILcom/example/mxextend/listener/IExtendCallback;)I
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/16 v0, 0x10

    if-nez p2, :cond_0

    .line 1565
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "setSpecialPoi:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 1569
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    .line 1570
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, ""

    invoke-direct {p1, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 1573
    :cond_1
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "setSpecialPoi: type="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1575
    :try_start_0
    iget-object v2, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v2, :cond_2

    invoke-interface {v2, p1}, Lcom/mxnavi/busines/ExtendServiceInterface;->collectCurrentPos(I)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    :goto_0
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 1576
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "setSpecialPoi: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1577
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v2, Lcom/example/mxextend/-$$Lambda$ExtendApi$08EnaLaqgGR_ZyLz1U2OztdQun4;

    invoke-direct {v2, p1, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$08EnaLaqgGR_ZyLz1U2OztdQun4;-><init>(ILcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v1, v2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p1

    .line 1585
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1586
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "setSpecialPoi: exception"

    invoke-static {p1, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1587
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$2NJoxykdtL2O8zF48EMmJwUirW4;

    invoke-direct {v1, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$2NJoxykdtL2O8zF48EMmJwUirW4;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_1
    return v0
.end method

.method public setVoiceBtnCallBack(Lcom/example/mxextend/listener/IVoiceBtnCallBack;)V
    .locals 0

    .line 2603
    iput-object p1, p0, Lcom/example/mxextend/ExtendApi;->voiceClickListener:Lcom/example/mxextend/listener/IVoiceBtnCallBack;

    return-void
.end method

.method public setVolumeMute(ZLcom/example/mxextend/listener/IExtendCallback;)I
    .locals 4
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(Z",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/16 v0, 0x11

    if-nez p2, :cond_0

    .line 2340
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "setVolumeMute:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 2343
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    .line 2344
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, ""

    invoke-direct {p1, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 2348
    :cond_1
    :try_start_0
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v1, :cond_2

    invoke-interface {v1, p1}, Lcom/mxnavi/busines/ExtendServiceInterface;->setVolumeMute(Z)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    :goto_0
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 2349
    sget-object v1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "setVolumeMute: resultcode="

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v1, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2350
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v2, Lcom/example/mxextend/-$$Lambda$ExtendApi$1916dJdq-i93UscU54lrsKYGe70;

    invoke-direct {v2, p1, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$1916dJdq-i93UscU54lrsKYGe70;-><init>(ILcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v1, v2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p1

    .line 2358
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 2359
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "setVolumeMute: exception"

    invoke-static {p1, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 2360
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$alOVmsoLhEbysWCxlVKRaMQTJeI;

    invoke-direct {v1, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$alOVmsoLhEbysWCxlVKRaMQTJeI;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_1
    return v0
.end method

.method public showFrontTraffic(Lcom/mxnavi/busines/entity/ShowFrontTrafficModel;Lcom/example/mxextend/listener/IExtendCallback;)I
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/mxnavi/busines/entity/ShowFrontTrafficModel;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/TrafficInfoModel;",
            ">;)I"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 1424
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "showFrontTraffic:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    const-string v0, ""

    .line 1427
    invoke-direct {p0, p1, v0, p2}, Lcom/example/mxextend/ExtendApi;->searchTraffic(ILjava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V

    :goto_0
    const/16 p1, 0x8

    return p1
.end method

.method public showInstrumentProjection(ILandroid/view/ViewGroup;)V
    .locals 3

    .line 894
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    .line 895
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "=====================\u4eea\u8868\u6295\u5c4f======================= screenType:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 893
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 897
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_0

    return-void

    .line 900
    :cond_0
    new-instance v1, Lcom/example/mxextend/widget/config/SurfaceMapConfig;

    invoke-direct {v1}, Lcom/example/mxextend/widget/config/SurfaceMapConfig;-><init>()V

    .line 901
    invoke-static {p2}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p2}, Landroid/view/ViewGroup;->getWidth()I

    move-result v2

    invoke-virtual {v1, v2}, Lcom/example/mxextend/widget/config/SurfaceMapConfig;->setMapWidth(I)V

    .line 902
    invoke-virtual {p2}, Landroid/view/ViewGroup;->getHeight()I

    move-result v2

    invoke-virtual {v1, v2}, Lcom/example/mxextend/widget/config/SurfaceMapConfig;->setMapHeight(I)V

    if-nez p1, :cond_3

    .line 904
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

    if-nez p1, :cond_1

    const-string p1, "initDisplaySurfaceView"

    .line 905
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 906
    iget p1, p0, Lcom/example/mxextend/ExtendApi;->mShowMode:I

    invoke-direct {p0, p1}, Lcom/example/mxextend/ExtendApi;->initDisplaySurfaceView(I)V

    .line 908
    :cond_1
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    .line 909
    iget-object v2, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    iput-object v2, p1, Lcom/example/mxextend/widget/DisplaySurfaceView;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    .line 910
    iget-boolean v2, p1, Lcom/example/mxextend/widget/DisplaySurfaceView;->isBind:Z

    if-nez v2, :cond_2

    const-string v2, "showInstrumentProjection: displaySurfaceView is Bind"

    .line 911
    invoke-static {v0, v2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 912
    invoke-virtual {p1}, Lcom/example/mxextend/widget/DisplaySurfaceView;->onBindSurfaceViewListener()V

    .line 914
    :cond_2
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "showInstrumentProjection: "

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v2, "DisplaySurfaceView"

    invoke-static {v2, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 915
    move-object v0, p1

    check-cast v0, Landroid/view/View;

    invoke-direct {p0, v0}, Lcom/example/mxextend/ExtendApi;->removeSelfFromParent(Landroid/view/View;)V

    .line 916
    invoke-virtual {p1, v1}, Lcom/example/mxextend/widget/DisplaySurfaceView;->setSurfaceMapConfig(Lcom/example/mxextend/widget/config/SurfaceMapConfig;)V

    .line 918
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

    check-cast p1, Landroid/view/View;

    .line 919
    new-instance v0, Landroid/view/ViewGroup$LayoutParams;

    const/4 v1, -0x1

    invoke-direct {v0, v1, v1}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    .line 917
    invoke-virtual {p2, p1, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    :cond_3
    return-void
.end method

.method public showMyLocation(IILcom/example/mxextend/listener/IExtendCallback;)I
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/LocationInfo;",
            ">;)I"
        }
    .end annotation

    const/4 v0, 0x3

    if-nez p3, :cond_0

    .line 811
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "showMyLocation:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 815
    :cond_0
    :try_start_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-nez v1, :cond_1

    goto :goto_0

    :cond_1
    if-eqz v1, :cond_3

    .line 823
    new-instance v2, Lcom/example/mxextend/ExtendApi$showMyLocation$1;

    invoke-direct {v2, p0, p3}, Lcom/example/mxextend/ExtendApi$showMyLocation$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v2, Lcom/mxnavi/busines/IServiceCallBack;

    .line 820
    invoke-interface {v1, p1, p2, v2}, Lcom/mxnavi/busines/ExtendServiceInterface;->getCurrentLocationWithOpera(IILcom/mxnavi/busines/IServiceCallBack;)V

    goto :goto_1

    .line 816
    :cond_2
    :goto_0
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 p2, -0x1

    const-string v1, "\u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {p1, p2, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 817
    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    return v0

    :catch_0
    move-exception p1

    .line 846
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 847
    new-instance p2, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x2

    invoke-virtual {p1}, Landroid/os/RemoteException;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-direct {p2, v1, p1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 848
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "showMyLocation: exception"

    invoke-static {p1, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 849
    invoke-interface {p3, p2}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    :cond_3
    :goto_1
    return v0
.end method

.method public showTraffic(Lcom/mxnavi/busines/entity/ShowTrafficModel;Lcom/example/mxextend/listener/IExtendCallback;)I
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/mxnavi/busines/entity/ShowTrafficModel;",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/TrafficInfoModel;",
            ">;)I"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 1411
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "showTraffic:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    :cond_0
    const/4 v0, 0x1

    .line 1414
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Lcom/mxnavi/busines/entity/ShowTrafficModel;->getTrafficText()Ljava/lang/String;

    move-result-object p1

    invoke-direct {p0, v0, p1, p2}, Lcom/example/mxextend/ExtendApi;->searchTraffic(ILjava/lang/String;Lcom/example/mxextend/listener/IExtendCallback;)V

    :goto_0
    const/4 p1, 0x4

    return p1
.end method

.method public specialPoiNavi(IILcom/example/mxextend/listener/IExtendCallback;)I
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/16 v0, 0xb

    if-nez p3, :cond_0

    .line 1502
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "specialPoiNavi:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 1506
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    .line 1507
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 p2, -0x1

    const-string v1, ""

    invoke-direct {p1, p2, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p3, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 1512
    :cond_1
    :try_start_0
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v1, :cond_2

    invoke-interface {v1, p1, p2}, Lcom/mxnavi/busines/ExtendServiceInterface;->goHomeOrCompany(II)I

    move-result p1

    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object p1

    goto :goto_0

    :cond_2
    const/4 p1, 0x0

    :goto_0
    invoke-static {p1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    invoke-virtual {p1}, Ljava/lang/Integer;->intValue()I

    move-result p1

    .line 1513
    sget-object p2, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "specialPoiNavi: resultcode="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {p2, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1514
    iget-object p2, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$eSlLPjVD9HhdayhQT1M6TTTPiL4;

    invoke-direct {v1, p1, p3}, Lcom/example/mxextend/-$$Lambda$ExtendApi$eSlLPjVD9HhdayhQT1M6TTTPiL4;-><init>(ILcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p2, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_1

    :catch_0
    move-exception p1

    .line 1522
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1523
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "specialPoiNavi: exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1524
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance p2, Lcom/example/mxextend/-$$Lambda$ExtendApi$zg7DebM4MhEGr-5XQQY970K9uVU;

    invoke-direct {p2, p3}, Lcom/example/mxextend/-$$Lambda$ExtendApi$zg7DebM4MhEGr-5XQQY970K9uVU;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, p2}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_1
    return v0
.end method

.method public specialPoiNavi(ILcom/example/mxextend/listener/IExtendCallback;)I
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)I"
        }
    .end annotation

    const/16 v0, 0xb

    if-nez p2, :cond_0

    .line 1534
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "specialPoiNavi2:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return v0

    .line 1537
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    .line 1538
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, ""

    invoke-direct {p1, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return v0

    .line 1542
    :cond_1
    :try_start_0
    iget-object v1, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    invoke-static {v1}, Lkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V

    new-instance v2, Lcom/example/mxextend/ExtendApi$specialPoiNavi$3;

    invoke-direct {v2, p2}, Lcom/example/mxextend/ExtendApi$specialPoiNavi$3;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v2, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v1, p1, v2}, Lcom/mxnavi/busines/ExtendServiceInterface;->goHomeOrCompany2(ILcom/mxnavi/busines/IServiceCallBack;)I
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 1554
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 1555
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "specialPoiNavi: exception"

    invoke-static {p1, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 1556
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$i8epPa3lsX24Qeq094Ty46zSpv4;

    invoke-direct {v1, p2}, Lcom/example/mxextend/-$$Lambda$ExtendApi$i8epPa3lsX24Qeq094Ty46zSpv4;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {p1, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :goto_0
    return v0
.end method

.method public startMXnaviAppResult(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/CarltdLoginResultBean;",
            ">;)V"
        }
    .end annotation

    .line 733
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u62c9\u8d77\u5bfc\u822aapp========================01.0.31"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    if-nez p1, :cond_0

    const-string p1, "\u62c9\u8d77\u5bfc\u822aapp:callback= null"

    .line 735
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 738
    :cond_0
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_1

    const-string v1, "\u62c9\u8d77\u5bfc\u822aapp:\u62c9\u8d77\u5bfc\u822aapp \u670d\u52a1\u672a\u542f\u52a8l"

    .line 739
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 741
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v1, -0x1

    const-string v2, "\u62c9\u8d77\u5bfc\u822aapp \u670d\u52a1\u672a\u542f\u52a8"

    invoke-direct {v0, v1, v2}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    .line 740
    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    return-void

    :cond_1
    :try_start_0
    const-string v1, "======================\u62c9\u8d77\u5bfc\u822aapp========================1"

    .line 749
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 750
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v1, Lcom/example/mxextend/ExtendApi$startMXnaviAppResult$1;

    invoke-direct {v1, p0, p1}, Lcom/example/mxextend/ExtendApi$startMXnaviAppResult$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v1, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, v1}, Lcom/mxnavi/busines/ExtendServiceInterface;->startMXnaviApp(Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 772
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 773
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "======================\u62c9\u8d77\u5bfc\u822aapp========================2"

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    const-string v1, "requestAddPass: exception="

    .line 774
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 775
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mainHandler:Landroid/os/Handler;

    new-instance v1, Lcom/example/mxextend/-$$Lambda$ExtendApi$Ro6qzG3s85Xr2ZX3CyWZY2xIWc8;

    invoke-direct {v1, p1}, Lcom/example/mxextend/-$$Lambda$ExtendApi$Ro6qzG3s85Xr2ZX3CyWZY2xIWc8;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    invoke-virtual {v0, v1}, Landroid/os/Handler;->post(Ljava/lang/Runnable;)Z

    :cond_2
    :goto_0
    return-void
.end method

.method public stopInstrumentProjection(I)V
    .locals 2

    .line 948
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "=====================\u4eea\u8868\u7ed3\u675f\u6295\u5c4f======================="

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 950
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v0, :cond_0

    return-void

    .line 955
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0, p1}, Lcom/mxnavi/busines/ExtendServiceInterface;->stopInstrumentProjection(I)V

    .line 956
    :cond_1
    iget-object p1, p0, Lcom/example/mxextend/ExtendApi;->displaySurfaceView:Lcom/example/mxextend/widget/DisplaySurfaceView;

    if-eqz p1, :cond_2

    invoke-virtual {p1}, Lcom/example/mxextend/widget/DisplaySurfaceView;->dissmissDifferentDislayGuideView()V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 958
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    :cond_2
    :goto_0
    return-void
.end method

.method public switchParallelRoad(ILcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(I",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    if-nez p2, :cond_0

    .line 3159
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "switchParallelRoad:callback= null"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 3160
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    const-string v1, ""

    if-nez v0, :cond_1

    .line 3161
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x1

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    goto :goto_0

    .line 3164
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v2, Lcom/example/mxextend/ExtendApi$switchParallelRoad$1;

    invoke-direct {v2, p2}, Lcom/example/mxextend/ExtendApi$switchParallelRoad$1;-><init>(Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v2, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, p1, v2}, Lcom/mxnavi/busines/ExtendServiceInterface;->switchParallelRoad(ILcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    .line 3175
    invoke-virtual {p1}, Landroid/os/RemoteException;->printStackTrace()V

    .line 3176
    new-instance p1, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v0, -0x2

    invoke-direct {p1, v0, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p2, p1}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    .line 3177
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string p2, "switchParallelRoad:exception"

    invoke-static {p1, p2}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :cond_2
    :goto_0
    return-void
.end method

.method public switchRoute(Lcom/example/mxextend/listener/IExtendCallback;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/example/mxextend/listener/IExtendCallback<",
            "Lcom/example/mxextend/entity/ExtendBaseModel;",
            ">;)V"
        }
    .end annotation

    if-nez p1, :cond_0

    .line 3184
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "switchParallelRoad:callback= null"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    goto :goto_0

    .line 3185
    :cond_0
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    const-string v1, ""

    if-nez v0, :cond_1

    .line 3186
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v2, -0x1

    invoke-direct {v0, v2, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    goto :goto_0

    .line 3189
    :cond_1
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_2

    new-instance v2, Lcom/example/mxextend/ExtendApi$switchRoute$1;

    invoke-direct {v2, p0, p1}, Lcom/example/mxextend/ExtendApi$switchRoute$1;-><init>(Lcom/example/mxextend/ExtendApi;Lcom/example/mxextend/listener/IExtendCallback;)V

    check-cast v2, Lcom/mxnavi/busines/IServiceCallBack;

    invoke-interface {v0, v2}, Lcom/mxnavi/busines/ExtendServiceInterface;->switchRoute(Lcom/mxnavi/busines/IServiceCallBack;)V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 3202
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    .line 3203
    new-instance v0, Lcom/example/mxextend/entity/ExtendErrorModel;

    const/4 v2, -0x2

    invoke-direct {v0, v2, v1}, Lcom/example/mxextend/entity/ExtendErrorModel;-><init>(ILjava/lang/String;)V

    invoke-interface {p1, v0}, Lcom/example/mxextend/listener/IExtendCallback;->onFail(Lcom/example/mxextend/entity/ExtendErrorModel;)V

    .line 3204
    sget-object p1, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v0, "switchParallelRoad:exception"

    invoke-static {p1, v0}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :cond_2
    :goto_0
    return-void
.end method

.method public transStsRequest()V
    .locals 2

    .line 963
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "transStsRequest: "

    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 964
    iget-boolean v1, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-nez v1, :cond_0

    const-string v1, "\u670d\u52a1\u672a\u542f\u52a8"

    .line 965
    invoke-static {v0, v1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    return-void

    .line 969
    :cond_0
    :try_start_0
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_1

    invoke-interface {v0}, Lcom/mxnavi/busines/ExtendServiceInterface;->transStsRequest()V
    :try_end_0
    .catch Landroid/os/RemoteException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    .line 971
    invoke-virtual {v0}, Landroid/os/RemoteException;->printStackTrace()V

    :cond_1
    :goto_0
    return-void
.end method

.method public unBindMxExtService()V
    .locals 2

    .line 244
    iget-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    if-eqz v0, :cond_0

    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    if-eqz v0, :cond_0

    .line 245
    sget-object v0, Lcom/example/mxextend/ExtendApi;->TAG:Ljava/lang/String;

    const-string v1, "unBindMxExtService"

    invoke-static {v0, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 246
    iget-object v0, p0, Lcom/example/mxextend/ExtendApi;->mServiceConnection:Landroid/content/ServiceConnection;

    invoke-direct {p0, v0}, Lcom/example/mxextend/ExtendApi;->unbindService(Landroid/content/ServiceConnection;)V

    const/4 v0, 0x0

    .line 247
    iput-object v0, p0, Lcom/example/mxextend/ExtendApi;->extendServiceInterface:Lcom/mxnavi/busines/ExtendServiceInterface;

    const/4 v0, 0x0

    .line 248
    iput-boolean v0, p0, Lcom/example/mxextend/ExtendApi;->isBind:Z

    :cond_0
    return-void
.end method
