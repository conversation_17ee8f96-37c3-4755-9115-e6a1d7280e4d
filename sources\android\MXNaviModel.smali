.class public Lcom/chinatsp/dashboard/model/MXNaviModel;
.super Ljava/lang/Object;
.source "MXNaviModel.java"

# interfaces
.implements Lcom/example/mxextend/listener/IServiceConnectedListener;
.implements Lcom/example/mxextend/listener/IExtendListener;
.implements Lcom/chinatsp/dashboard/model/InteractiveModel$INaviProtocol;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/chinatsp/dashboard/model/MXNaviModel$LocationCallback;,
        Lcom/chinatsp/dashboard/model/MXNaviModel$OnGetLocResultCallback;
    }
.end annotation


# static fields
.field private static final DELAY_NAVI_INFO:I = 0xf

.field private static final NAVI_LENGTH:I = 0xc

.field private static final TAG:Ljava/lang/String; = "MXNaviModel"


# instance fields
.field private volatile interactiveProtocol:Lcom/chinatsp/dashboard/model/IInteractiveProtocol;

.field private final isConnected:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private final mContext:Landroid/content/Context;

.field private mExtendApi:Lcom/example/mxextend/IExtendApi;

.field private mLastCurDis:I

.field private mLastTotalTime:I

.field private mNaviCallback:Lcom/chinatsp/dashboard/callback/NaviCallback;

.field private final mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

.field private final naviInfoBean:Lcom/chinatsp/dashboard/bean/NaviInfoBean;


# direct methods
.method public constructor <init>(Landroid/content/Context;Lcom/chinatsp/dashboard/callback/NaviCallback;)V
    .locals 2

    .line 77
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 59
    new-instance v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    .line 70
    new-instance v0, Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-direct {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;-><init>(Z)V

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->isConnected:Ljava/util/concurrent/atomic/AtomicBoolean;

    .line 74
    new-instance v0, Lcom/chinatsp/dashboard/bean/NaviInfoBean;

    invoke-direct {v0}, Lcom/chinatsp/dashboard/bean/NaviInfoBean;-><init>()V

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->naviInfoBean:Lcom/chinatsp/dashboard/bean/NaviInfoBean;

    .line 78
    invoke-virtual {p1}, Landroid/content/Context;->getApplicationContext()Landroid/content/Context;

    move-result-object p1

    iput-object p1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mContext:Landroid/content/Context;

    .line 79
    invoke-virtual {p0, p2}, Lcom/chinatsp/dashboard/model/MXNaviModel;->setNaviCallback(Lcom/chinatsp/dashboard/callback/NaviCallback;)V

    return-void
.end method

.method private actualSendNaviInfo(IIIIILjava/lang/String;Ljava/lang/String;)V
    .locals 2

    .line 280
    iput p3, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mLastCurDis:I

    .line 281
    iput p2, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mLastTotalTime:I

    .line 282
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "totalDis = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", totalTime = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", curDis = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", iconNum = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " , nextRoadName:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " , originIconNum:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p5

    const-string v0, " , nextCrossInfo:"

    invoke-virtual {p5, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p5

    invoke-virtual {p5, p6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p5

    invoke-virtual {p5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p5

    invoke-static {p5}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    const/16 p5, 0xc

    new-array p5, p5, [I

    const p6, 0x1fbd1

    .line 304
    invoke-static {p3, p6}, Ljava/lang/Math;->min(II)I

    move-result p3

    const/4 p6, 0x0

    aput p3, p5, p6

    const/4 p3, 0x1

    aput p4, p5, p3

    const/4 p4, 0x2

    aput p6, p5, p4

    const/4 p4, 0x3

    aput p1, p5, p4

    .line 309
    invoke-direct {p0, p2}, Lcom/chinatsp/dashboard/model/MXNaviModel;->getDay(I)I

    move-result p1

    .line 310
    invoke-direct {p0, p2}, Lcom/chinatsp/dashboard/model/MXNaviModel;->getHour(I)I

    move-result p4

    .line 311
    invoke-direct {p0, p2}, Lcom/chinatsp/dashboard/model/MXNaviModel;->getMinute(I)I

    move-result p6

    const/16 v0, 0x3c

    if-ge p2, v0, :cond_0

    if-lez p2, :cond_0

    const-string p2, "totalTime < 60 && totalTime > 0), return min = 1"

    .line 315
    invoke-static {p2}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    move p6, p3

    :cond_0
    const/4 p2, 0x4

    aput p1, p5, p2

    const/4 p1, 0x5

    aput p4, p5, p1

    const/4 p1, 0x6

    aput p6, p5, p1

    .line 320
    invoke-direct {p0, p7}, Lcom/chinatsp/dashboard/model/MXNaviModel;->sendNextRoadName(Ljava/lang/String;)V

    const/4 p1, 0x0

    .line 321
    invoke-direct {p0, p5, p3, p1}, Lcom/chinatsp/dashboard/model/MXNaviModel;->actualSendNaviMultiFrame([II[C)V

    return-void
.end method

.method private actualSendNaviMultiFrame([II[C)V
    .locals 9

    if-eqz p1, :cond_7

    .line 359
    array-length v0, p1

    const/16 v1, 0xc

    if-ge v0, v1, :cond_0

    goto :goto_4

    :cond_0
    const/16 v0, 0x8

    const/16 v1, 0xb

    const/16 v2, 0xa

    const/16 v3, 0x9

    const/4 v4, 0x7

    const/4 v5, 0x1

    const/4 v6, 0x0

    if-nez p3, :cond_1

    .line 364
    aput v6, p1, v4

    .line 365
    aput v6, p1, v0

    .line 366
    aput v6, p1, v3

    .line 367
    aput v6, p1, v2

    .line 368
    aput v6, p1, v1

    goto :goto_3

    .line 371
    :cond_1
    array-length v7, p3

    mul-int/lit8 v8, p2, 0x3

    if-lt v8, v7, :cond_2

    const/16 v8, 0xf

    .line 374
    aput v8, p1, v4

    goto :goto_0

    .line 377
    :cond_2
    aput p2, p1, v4

    move v5, v6

    .line 379
    :goto_0
    aput v7, p1, v0

    add-int/lit8 v0, p2, -0x1

    mul-int/lit8 v0, v0, 0x3

    if-le v7, v0, :cond_3

    .line 381
    aget-char v4, p3, v0

    aput v4, p1, v3

    goto :goto_1

    .line 383
    :cond_3
    aput v6, p1, v3

    :goto_1
    add-int/lit8 v3, v0, 0x1

    if-le v7, v3, :cond_4

    .line 386
    aget-char v3, p3, v3

    aput v3, p1, v2

    goto :goto_2

    .line 388
    :cond_4
    aput v6, p1, v2

    :goto_2
    add-int/lit8 v0, v0, 0x2

    if-le v7, v0, :cond_5

    .line 391
    aget-char v0, p3, v0

    aput v0, p1, v1

    goto :goto_3

    .line 393
    :cond_5
    aput v6, p1, v1

    .line 396
    :goto_3
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviCallback:Lcom/chinatsp/dashboard/callback/NaviCallback;

    if-eqz v0, :cond_6

    .line 397
    invoke-interface {v0, p1}, Lcom/chinatsp/dashboard/callback/NaviCallback;->requestSendNaviInfo([I)V

    :cond_6
    if-nez v5, :cond_7

    .line 400
    invoke-static {}, Lcom/chinatsp/dashboard/utils/HandlerUtil;->getThreadHandler()Landroid/os/Handler;

    move-result-object v0

    new-instance v1, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$qhFQetl3CeJx3RRW5JvkZa3oW2U;

    invoke-direct {v1, p0, p1, p2, p3}, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$qhFQetl3CeJx3RRW5JvkZa3oW2U;-><init>(Lcom/chinatsp/dashboard/model/MXNaviModel;[II[C)V

    const-wide/16 p1, 0xf

    invoke-virtual {v0, v1, p1, p2}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    :cond_7
    :goto_4
    return-void
.end method

.method private declared-synchronized fixState()V
    .locals 3

    monitor-enter p0

    .line 432
    :try_start_0
    monitor-enter p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    .line 433
    :try_start_1
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    if-nez v0, :cond_0

    .line 434
    invoke-static {}, Lcom/example/mxextend/TAExtendManager;->getInstance()Lcom/example/mxextend/TAExtendManager;

    move-result-object v0

    invoke-virtual {v0}, Lcom/example/mxextend/TAExtendManager;->getExtendApi()Lcom/example/mxextend/IExtendApi;

    move-result-object v0

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    const-string v0, "MXNaviModel"

    const-string v1, "fixState mExtendApi == null."

    .line 435
    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 437
    :cond_0
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->isConnected:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    .line 438
    iget-object v1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    .line 439
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-nez v0, :cond_1

    :try_start_2
    const-string v0, "MXNaviModel"

    const-string v2, "fixState isConnected is false."

    .line 441
    invoke-static {v0, v2}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    if-eqz v1, :cond_1

    .line 443
    invoke-interface {v1}, Lcom/example/mxextend/IExtendApi;->bindMxExtService()V

    .line 446
    :cond_1
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->isConnected:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicBoolean;->compareAndSet(ZZ)Z
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 447
    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    .line 439
    :try_start_3
    monitor-exit p0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    :try_start_4
    throw v0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    :catchall_1
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method private getDay(I)I
    .locals 1

    const v0, 0x15180

    .line 405
    div-int/2addr p1, v0

    return p1
.end method

.method private getHour(I)I
    .locals 1

    const v0, 0x15180

    .line 409
    rem-int/2addr p1, v0

    div-int/lit16 p1, p1, 0xe10

    return p1
.end method

.method private getMinute(I)I
    .locals 3

    .line 415
    rem-int/lit16 p1, p1, 0xe10

    .line 417
    rem-int/lit8 v0, p1, 0x3c

    .line 418
    div-int/lit8 p1, p1, 0x3c

    const/16 v1, 0x1e

    if-lt v0, v1, :cond_0

    .line 420
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "minute= "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v2, " second="

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    add-int/lit8 p1, p1, 0x1

    :cond_0
    return p1
.end method

.method private isSimulation()Z
    .locals 3

    .line 462
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    invoke-interface {v0}, Lcom/example/mxextend/IExtendApi;->getNaviType()I

    move-result v0

    .line 463
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, " naviType: "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "MXNaviModel"

    invoke-static {v2, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v1, 0x1

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    return v1
.end method

.method private naviStateChange(Z)V
    .locals 4

    .line 88
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "naviStateChange isRunning:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " , mNaviCallback:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviCallback:Lcom/chinatsp/dashboard/callback/NaviCallback;

    const/4 v2, 0x0

    const/4 v3, 0x1

    if-eqz v1, :cond_0

    move v1, v3

    goto :goto_0

    :cond_0
    move v1, v2

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " , interactiveProtocol:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->interactiveProtocol:Lcom/chinatsp/dashboard/model/IInteractiveProtocol;

    if-eqz v1, :cond_1

    move v2, v3

    :cond_1
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "MXNaviModel"

    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 93
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0, p1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    if-nez p1, :cond_2

    .line 95
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->naviInfoBean:Lcom/chinatsp/dashboard/bean/NaviInfoBean;

    invoke-virtual {v0}, Lcom/chinatsp/dashboard/bean/NaviInfoBean;->clear()V

    .line 99
    :cond_2
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mContext:Landroid/content/Context;

    invoke-virtual {v0}, Landroid/content/Context;->getContentResolver()Landroid/content/ContentResolver;

    move-result-object v0

    xor-int/lit8 v1, p1, 0x1

    const-string v2, "navi_status_to_vehicle_control"

    invoke-static {v0, v2, v1}, Landroid/provider/Settings$System;->putInt(Landroid/content/ContentResolver;Ljava/lang/String;I)Z

    .line 100
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviCallback:Lcom/chinatsp/dashboard/callback/NaviCallback;

    if-eqz v0, :cond_3

    .line 101
    invoke-interface {v0, p1}, Lcom/chinatsp/dashboard/callback/NaviCallback;->naviStateChange(Z)V

    .line 103
    :cond_3
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->interactiveProtocol:Lcom/chinatsp/dashboard/model/IInteractiveProtocol;

    if-eqz v0, :cond_4

    .line 104
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->interactiveProtocol:Lcom/chinatsp/dashboard/model/IInteractiveProtocol;

    invoke-interface {v0, p1}, Lcom/chinatsp/dashboard/model/IInteractiveProtocol;->naviStateChange(Z)V

    :cond_4
    return-void
.end method

.method private sendNaviInfo(IIIIILjava/lang/String;Ljava/lang/String;Z)V
    .locals 14

    move-object v9, p0

    move/from16 v10, p2

    move/from16 v11, p3

    .line 249
    iget-object v0, v9, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-nez v0, :cond_0

    const-string v0, "sendNaviInfo fail. navi was stop."

    .line 250
    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->w(Ljava/lang/String;)V

    return-void

    :cond_0
    if-nez p1, :cond_1

    if-nez v10, :cond_1

    const-string v0, "totalDis == 0 && totalTime == 0, return"

    .line 254
    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->e(Ljava/lang/String;)V

    return-void

    :cond_1
    if-eqz p8, :cond_2

    .line 257
    iget v0, v9, Lcom/chinatsp/dashboard/model/MXNaviModel;->mLastCurDis:I

    if-ne v11, v0, :cond_2

    iget v0, v9, Lcom/chinatsp/dashboard/model/MXNaviModel;->mLastTotalTime:I

    if-ne v10, v0, :cond_2

    .line 258
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "bootInit = false && curDis == mLastCurDis = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v11}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, " && totalTime == mLastTotalTime = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, v10}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v0

    const-string v1, ", return"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->e(Ljava/lang/String;)V

    return-void

    .line 262
    :cond_2
    iget-object v0, v9, Lcom/chinatsp/dashboard/model/MXNaviModel;->naviInfoBean:Lcom/chinatsp/dashboard/bean/NaviInfoBean;

    move v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move-object/from16 v4, p7

    move/from16 v5, p4

    invoke-virtual/range {v0 .. v5}, Lcom/chinatsp/dashboard/bean/NaviInfoBean;->needUpdate(IIILjava/lang/String;I)Z

    move-result v0

    if-eqz v0, :cond_3

    .line 263
    invoke-static {}, Lcom/chinatsp/dashboard/utils/HandlerUtil;->getThreadHandler()Landroid/os/Handler;

    move-result-object v12

    new-instance v13, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$3_mwWxc5PK7GAnhUYRscVnTTA5Y;

    move-object v0, v13

    move-object v1, p0

    move v2, p1

    move/from16 v3, p2

    move/from16 v4, p3

    move/from16 v5, p4

    move/from16 v6, p5

    move-object/from16 v7, p6

    move-object/from16 v8, p7

    invoke-direct/range {v0 .. v8}, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$3_mwWxc5PK7GAnhUYRscVnTTA5Y;-><init>(Lcom/chinatsp/dashboard/model/MXNaviModel;IIIIILjava/lang/String;Ljava/lang/String;)V

    const-wide/16 v0, 0xf

    invoke-virtual {v12, v13, v0, v1}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    .line 264
    iget-object v0, v9, Lcom/chinatsp/dashboard/model/MXNaviModel;->naviInfoBean:Lcom/chinatsp/dashboard/bean/NaviInfoBean;

    move v1, p1

    move/from16 v2, p2

    move/from16 v3, p3

    move-object/from16 v4, p7

    invoke-virtual/range {v0 .. v5}, Lcom/chinatsp/dashboard/bean/NaviInfoBean;->update(IIILjava/lang/String;I)V

    :cond_3
    return-void
.end method

.method private sendNextRoadName(Ljava/lang/String;)V
    .locals 4

    .line 330
    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    .line 333
    :cond_0
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    mul-int/lit8 v0, v0, 0x2

    add-int/lit8 v1, v0, 0x1

    .line 336
    new-array v2, v1, [B

    int-to-byte v0, v0

    const/4 v3, 0x0

    .line 338
    aput-byte v0, v2, v3

    .line 342
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/DataUtil;->strToUnicode(Ljava/lang/String;)Ljava/util/List;

    move-result-object p1

    .line 343
    invoke-interface {p1}, Ljava/util/List;->size()I

    :goto_0
    add-int/lit8 v0, v1, -0x1

    if-ge v3, v0, :cond_1

    add-int/lit8 v0, v3, 0x1

    .line 346
    invoke-interface {p1, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ljava/lang/Byte;

    invoke-virtual {v3}, Ljava/lang/Byte;->byteValue()B

    move-result v3

    aput-byte v3, v2, v0

    move v3, v0

    goto :goto_0

    .line 348
    :cond_1
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviCallback:Lcom/chinatsp/dashboard/callback/NaviCallback;

    invoke-interface {p1, v2}, Lcom/chinatsp/dashboard/callback/NaviCallback;->requestSendNextRoadName([B)V

    return-void
.end method


# virtual methods
.method public init()V
    .locals 10

    .line 113
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "isConnected="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->isConnected:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 114
    invoke-static {}, Lcom/example/mxextend/TAExtendManager;->getInstance()Lcom/example/mxextend/TAExtendManager;

    move-result-object v0

    iget-object v1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mContext:Landroid/content/Context;

    invoke-virtual {v0, v1}, Lcom/example/mxextend/TAExtendManager;->init(Landroid/content/Context;)V

    .line 115
    invoke-static {}, Lcom/example/mxextend/TAExtendManager;->getInstance()Lcom/example/mxextend/TAExtendManager;

    move-result-object v0

    invoke-virtual {v0}, Lcom/example/mxextend/TAExtendManager;->getApi()Lcom/example/mxextend/IExtendApi;

    move-result-object v0

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    .line 116
    invoke-interface {v0, p0}, Lcom/example/mxextend/IExtendApi;->addServiceConnectedListener(Lcom/example/mxextend/listener/IServiceConnectedListener;)V

    .line 117
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    invoke-interface {v0, p0}, Lcom/example/mxextend/IExtendApi;->addExtendListener(Lcom/example/mxextend/listener/IExtendListener;)V

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    const/4 v8, 0x0

    const/4 v9, 0x1

    move-object v1, p0

    .line 120
    invoke-direct/range {v1 .. v9}, Lcom/chinatsp/dashboard/model/MXNaviModel;->sendNaviInfo(IIIIILjava/lang/String;Ljava/lang/String;Z)V

    return-void
.end method

.method public isNaviRunning()Z
    .locals 8

    .line 470
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    .line 471
    iget-object v1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    const/4 v2, 0x0

    const-string v3, "MXNaviModel"

    if-eqz v1, :cond_2

    iget-object v1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->isConnected:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v1

    if-eqz v1, :cond_2

    .line 472
    iget-object v1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    invoke-interface {v1}, Lcom/example/mxextend/IExtendApi;->getNaviState()I

    move-result v1

    const/4 v4, 0x2

    const/4 v5, 0x1

    if-ne v1, v4, :cond_0

    if-eqz v0, :cond_0

    move v4, v5

    goto :goto_0

    :cond_0
    move v4, v2

    .line 475
    :goto_0
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    const-string v7, "isNaviStarted="

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v6

    const-string v7, " isConnected="

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    iget-object v7, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->isConnected:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v6

    const-string v7, " naviState="

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v6

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    invoke-static {v3, v6}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    if-ne v1, v5, :cond_1

    if-eqz v0, :cond_1

    .line 478
    iget-object v1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v1, v5, v2}, Ljava/util/concurrent/atomic/AtomicBoolean;->compareAndSet(ZZ)Z

    const-string v1, "mNaviStarted value abnormal. update mNaviStarted value and reset icon num 0."

    .line 479
    invoke-static {v3, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 480
    invoke-static {}, Lcom/chinatsp/dashboard/utils/HandlerUtil;->getThreadHandler()Landroid/os/Handler;

    move-result-object v1

    new-instance v2, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$qijkqq2TIv3E6o3Io--JajbdkbQ;

    invoke-direct {v2, p0}, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$qijkqq2TIv3E6o3Io--JajbdkbQ;-><init>(Lcom/chinatsp/dashboard/model/MXNaviModel;)V

    const-wide/16 v5, 0xf

    invoke-virtual {v1, v2, v5, v6}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    :cond_1
    move v2, v4

    goto :goto_1

    .line 484
    :cond_2
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "mExtendApi= "

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    iget-object v4, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v3, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 486
    :goto_1
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "isNaviRunning result:"

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v1

    const-string v4, " , isNaviStarted:"

    invoke-virtual {v1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v3, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v2
.end method

.method public isNaviStarted()Z
    .locals 1

    .line 450
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    return v0
.end method

.method public synthetic lambda$actualSendNaviMultiFrame$4$MXNaviModel([II[C)V
    .locals 0

    add-int/lit8 p2, p2, 0x1

    .line 400
    invoke-direct {p0, p1, p2, p3}, Lcom/chinatsp/dashboard/model/MXNaviModel;->actualSendNaviMultiFrame([II[C)V

    return-void
.end method

.method public synthetic lambda$isNaviRunning$5$MXNaviModel()V
    .locals 8

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    move-object v0, p0

    .line 480
    invoke-direct/range {v0 .. v7}, Lcom/chinatsp/dashboard/model/MXNaviModel;->actualSendNaviInfo(IIIIILjava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic lambda$onModelReceived$1$MXNaviModel()V
    .locals 8

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    move-object v0, p0

    .line 185
    invoke-direct/range {v0 .. v7}, Lcom/chinatsp/dashboard/model/MXNaviModel;->actualSendNaviInfo(IIIIILjava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic lambda$onModelReceived$2$MXNaviModel()V
    .locals 8

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    move-object v0, p0

    .line 196
    invoke-direct/range {v0 .. v7}, Lcom/chinatsp/dashboard/model/MXNaviModel;->actualSendNaviInfo(IIIIILjava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic lambda$onServiceConnected$0$MXNaviModel()V
    .locals 8

    const/4 v1, 0x0

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/4 v6, 0x0

    const/4 v7, 0x0

    move-object v0, p0

    .line 142
    invoke-direct/range {v0 .. v7}, Lcom/chinatsp/dashboard/model/MXNaviModel;->actualSendNaviInfo(IIIIILjava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic lambda$sendNaviInfo$3$MXNaviModel(IIIIILjava/lang/String;Ljava/lang/String;)V
    .locals 0

    .line 263
    invoke-direct/range {p0 .. p7}, Lcom/chinatsp/dashboard/model/MXNaviModel;->actualSendNaviInfo(IIIIILjava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public onJSONReceived(Lorg/json/JSONObject;)V
    .locals 2

    .line 159
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "json = "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    return-void
.end method

.method public onModelReceived(Lcom/example/mxextend/entity/ExtendBaseModel;)V
    .locals 9

    .line 164
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/MXNaviModel;->fixState()V

    if-nez p1, :cond_0

    return-void

    .line 168
    :cond_0
    invoke-virtual {p1}, Lcom/example/mxextend/entity/ExtendBaseModel;->getExtendId()I

    move-result v0

    const/16 v1, 0x1a

    const/4 v2, 0x2

    const/4 v3, 0x1

    if-eq v0, v1, :cond_6

    const/16 v1, 0x1b

    if-eq v0, v1, :cond_1

    goto/16 :goto_0

    .line 206
    :cond_1
    instance-of v0, p1, Lcom/example/mxextend/entity/GuideInfoModel;

    if-nez v0, :cond_2

    return-void

    .line 209
    :cond_2
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    if-eqz v0, :cond_3

    invoke-interface {v0}, Lcom/example/mxextend/IExtendApi;->getNaviState()I

    move-result v0

    if-ne v0, v2, :cond_3

    .line 210
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v0

    if-nez v0, :cond_3

    .line 211
    invoke-direct {p0, v3}, Lcom/chinatsp/dashboard/model/MXNaviModel;->naviStateChange(Z)V

    .line 218
    :cond_3
    check-cast p1, Lcom/example/mxextend/entity/GuideInfoModel;

    .line 219
    invoke-virtual {p1}, Lcom/example/mxextend/entity/GuideInfoModel;->getRouteRemainDis()I

    move-result v1

    .line 220
    invoke-virtual {p1}, Lcom/example/mxextend/entity/GuideInfoModel;->getRouteRemainTime()I

    move-result v2

    .line 221
    invoke-virtual {p1}, Lcom/example/mxextend/entity/GuideInfoModel;->getSegRemainDis()I

    move-result v3

    .line 222
    invoke-virtual {p1}, Lcom/example/mxextend/entity/GuideInfoModel;->getIcon()I

    move-result v5

    .line 223
    invoke-virtual {p1}, Lcom/example/mxextend/entity/GuideInfoModel;->getNextRoadName()Ljava/lang/String;

    move-result-object v7

    .line 224
    invoke-virtual {p1}, Lcom/example/mxextend/entity/GuideInfoModel;->getNextCrossInfo()Ljava/lang/String;

    move-result-object v6

    .line 225
    new-instance p1, Ljava/lang/StringBuilder;

    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v0, "set navi iconNum= "

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " nextRoadName="

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v4, " totalTime="

    invoke-virtual {p1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v4, " curDis="

    invoke-virtual {p1, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " nextCrossInfo="

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "MXNaviModel"

    invoke-static {v0, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 231
    invoke-static {v6}, Lcom/chinatsp/dashboard/utils/DataUtil;->parseCrossCount(Ljava/lang/String;)I

    move-result p1

    invoke-static {p1, v5}, Lcom/chinatsp/dashboard/utils/DataUtil;->naviIconConvert(II)I

    move-result p1

    .line 232
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v8, "set navi huNaviIconNum= "

    invoke-virtual {v4, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v4

    const-string v8, " iconNum="

    invoke-virtual {v4, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v4

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v0, v4}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    if-ne p1, v5, :cond_4

    .line 234
    invoke-static {v5}, Lcom/chinatsp/dashboard/utils/DataUtil;->naviIconConvert(I)I

    move-result p1

    :cond_4
    move v4, p1

    if-nez v4, :cond_5

    .line 236
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {p1}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result p1

    if-eqz p1, :cond_5

    const-string p1, "set navi icon num is 0x80(invalid value)"

    .line 238
    invoke-static {v0, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_5
    const/4 v8, 0x0

    move-object v0, p0

    .line 240
    invoke-direct/range {v0 .. v8}, Lcom/chinatsp/dashboard/model/MXNaviModel;->sendNaviInfo(IIIIILjava/lang/String;Ljava/lang/String;Z)V

    goto :goto_0

    .line 171
    :cond_6
    instance-of v0, p1, Lcom/example/mxextend/entity/ExtendTAStatusModel;

    if-nez v0, :cond_7

    return-void

    .line 174
    :cond_7
    check-cast p1, Lcom/example/mxextend/entity/ExtendTAStatusModel;

    .line 175
    invoke-virtual {p1}, Lcom/example/mxextend/entity/ExtendTAStatusModel;->getTAStatus()I

    move-result p1

    if-eq p1, v3, :cond_c

    const-wide/16 v0, 0xf

    const/4 v4, 0x0

    if-eq p1, v2, :cond_b

    const/4 v2, 0x3

    if-eq p1, v2, :cond_a

    const/4 v2, 0x4

    if-eq p1, v2, :cond_9

    const/4 v0, 0x5

    if-eq p1, v0, :cond_8

    goto :goto_0

    :cond_8
    const-string p1, "\u8fdb\u5165\u8def\u7ebf\u89c4\u5212\u9875\u9762"

    .line 199
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    goto :goto_0

    :cond_9
    const-string p1, "Simulation of navigation stop"

    .line 194
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 195
    invoke-direct {p0, v4}, Lcom/chinatsp/dashboard/model/MXNaviModel;->naviStateChange(Z)V

    .line 196
    invoke-static {}, Lcom/chinatsp/dashboard/utils/HandlerUtil;->getThreadHandler()Landroid/os/Handler;

    move-result-object p1

    new-instance v2, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$7ORCNLy5tyES7f6L6bqAbCqJsyI;

    invoke-direct {v2, p0}, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$7ORCNLy5tyES7f6L6bqAbCqJsyI;-><init>(Lcom/chinatsp/dashboard/model/MXNaviModel;)V

    invoke-virtual {p1, v2, v0, v1}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    goto :goto_0

    :cond_a
    const-string p1, "Simulation of navigation start"

    .line 189
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 190
    invoke-direct {p0, v3}, Lcom/chinatsp/dashboard/model/MXNaviModel;->naviStateChange(Z)V

    goto :goto_0

    :cond_b
    const-string p1, "Navigation stop"

    .line 183
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 184
    invoke-direct {p0, v4}, Lcom/chinatsp/dashboard/model/MXNaviModel;->naviStateChange(Z)V

    .line 185
    invoke-static {}, Lcom/chinatsp/dashboard/utils/HandlerUtil;->getThreadHandler()Landroid/os/Handler;

    move-result-object p1

    new-instance v2, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$nv6dxQ7MU9ZUS_w6l6qhdPh8L4s;

    invoke-direct {v2, p0}, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$nv6dxQ7MU9ZUS_w6l6qhdPh8L4s;-><init>(Lcom/chinatsp/dashboard/model/MXNaviModel;)V

    invoke-virtual {p1, v2, v0, v1}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    goto :goto_0

    :cond_c
    const-string p1, "Navigation start"

    .line 178
    invoke-static {p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 179
    invoke-direct {p0, v3}, Lcom/chinatsp/dashboard/model/MXNaviModel;->naviStateChange(Z)V

    :goto_0
    return-void
.end method

.method public onServiceConnected()V
    .locals 4

    .line 135
    invoke-static {}, Lcom/chinatsp/dashboard/utils/Logcat;->d()V

    .line 136
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->isConnected:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    .line 137
    invoke-direct {p0}, Lcom/chinatsp/dashboard/model/MXNaviModel;->fixState()V

    .line 138
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    if-eqz v0, :cond_1

    .line 139
    invoke-interface {v0}, Lcom/example/mxextend/IExtendApi;->getNaviState()I

    move-result v0

    const/4 v2, 0x2

    if-ne v0, v2, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    .line 140
    :goto_0
    invoke-direct {p0, v1}, Lcom/chinatsp/dashboard/model/MXNaviModel;->naviStateChange(Z)V

    if-nez v1, :cond_2

    .line 142
    invoke-static {}, Lcom/chinatsp/dashboard/utils/HandlerUtil;->getThreadHandler()Landroid/os/Handler;

    move-result-object v0

    new-instance v1, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$QJepmDUG5bq3w16DFI73AXMHZJA;

    invoke-direct {v1, p0}, Lcom/chinatsp/dashboard/model/-$$Lambda$MXNaviModel$QJepmDUG5bq3w16DFI73AXMHZJA;-><init>(Lcom/chinatsp/dashboard/model/MXNaviModel;)V

    const-wide/16 v2, 0xf

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    goto :goto_1

    .line 145
    :cond_1
    invoke-static {}, Lcom/example/mxextend/TAExtendManager;->getInstance()Lcom/example/mxextend/TAExtendManager;

    move-result-object v0

    invoke-virtual {v0}, Lcom/example/mxextend/TAExtendManager;->getExtendApi()Lcom/example/mxextend/IExtendApi;

    move-result-object v0

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    :cond_2
    :goto_1
    return-void
.end method

.method public onServiceDisconnected()V
    .locals 2

    const-string v0, "onServiceDisconnected"

    .line 151
    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 152
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->isConnected:Ljava/util/concurrent/atomic/AtomicBoolean;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    .line 153
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviStarted:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v0, v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->set(Z)V

    return-void
.end method

.method public release()V
    .locals 2

    .line 124
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "isConnected="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    iget-object v1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->isConnected:Ljava/util/concurrent/atomic/AtomicBoolean;

    invoke-virtual {v1}, Ljava/util/concurrent/atomic/AtomicBoolean;->get()Z

    move-result v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;)V

    .line 125
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    if-eqz v0, :cond_0

    .line 126
    invoke-interface {v0, p0}, Lcom/example/mxextend/IExtendApi;->removeServiceConnectedListener(Lcom/example/mxextend/listener/IServiceConnectedListener;)V

    .line 127
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    invoke-interface {v0, p0}, Lcom/example/mxextend/IExtendApi;->removeExtendListener(Lcom/example/mxextend/listener/IExtendListener;)V

    .line 128
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mExtendApi:Lcom/example/mxextend/IExtendApi;

    invoke-interface {v0}, Lcom/example/mxextend/IExtendApi;->unBindMxExtService()V

    :cond_0
    return-void
.end method

.method public setInteractiveProtocol(Lcom/chinatsp/dashboard/model/IInteractiveProtocol;)V
    .locals 3

    const-string v0, "MXNaviModel"

    .line 492
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "setInteractiveProtocol protocol:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 493
    monitor-enter p0

    .line 494
    :try_start_0
    iput-object p1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->interactiveProtocol:Lcom/chinatsp/dashboard/model/IInteractiveProtocol;

    .line 495
    monitor-exit p0

    return-void

    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    throw p1
.end method

.method public setNaviCallback(Lcom/chinatsp/dashboard/callback/NaviCallback;)V
    .locals 0

    .line 109
    iput-object p1, p0, Lcom/chinatsp/dashboard/model/MXNaviModel;->mNaviCallback:Lcom/chinatsp/dashboard/callback/NaviCallback;

    return-void
.end method
