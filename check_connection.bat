@echo off
chcp 65001 >nul
title Проверка подключения ADB

echo ========================================
echo 🔍 ПРОВЕРКА ПОДКЛЮЧЕНИЯ ADB
echo ========================================
echo.

echo 📱 Поиск подключенных устройств...
adb devices -l
echo.

echo 🔧 Информация о ADB:
adb version
echo.

echo 📋 Проверка Dashboard приложения...
adb shell ps | findstr dashboard
echo.

echo 🎯 Проверка системных настроек навигации...
adb shell settings list system | findstr navi
echo.

echo ========================================
echo 💡 ИНСТРУКЦИИ:
echo.
echo Если устройство не найдено:
echo 1. Включите отладку по USB
echo 2. Подключите USB кабель
echo 3. Разрешите отладку на устройстве
echo.
echo Если Dashboard не запущен:
echo 1. Запустите приложение Dashboard
echo 2. Убедитесь что оно работает в фоне
echo ========================================
echo.
pause
