@echo off
chcp 65001 >nul

REM Скрипт для активации/деактивации режима навигации на приборной панели
REM Использование: activate_navi.bat [on|off]

if "%1"=="" (
    echo Использование: %0 [on^|off]
    echo   on  - активировать режим навигации ^(включить трансляцию^)
    echo   off - деактивировать режим навигации ^(выключить трансляцию^)
    exit /b 1
)

if /i "%1"=="on" (
    echo 🚀 Активация режима навигации...
    adb shell am broadcast -a com.chinatsp.dashboard.FORCE_NAVI_ACTIVE --ez navi_state true
    echo ✅ Команда отправлена: режим навигации ВКЛЮЧЕН
    goto end
)

if /i "%1"=="off" (
    echo 🛑 Деактивация режима навигации...
    adb shell am broadcast -a com.chinatsp.dashboard.FORCE_NAVI_ACTIVE --ez navi_state false
    echo ✅ Команда отправлена: режим навигации ВЫКЛЮЧЕН
    goto end
)

echo ❌ Неверный параметр: %1
echo Используйте 'on' или 'off'
exit /b 1

:end
echo.
echo 📱 Проверьте приборную панель - она должна переключиться в соответствующий режим
echo 📋 Для просмотра логов: adb logcat ^| findstr "YandexNaviModel DashboardModel InteractiveModel"
pause
