@echo off
chcp 65001 >nul

REM Скрипт для активации/деактивации режима навигации на приборной панели
REM Использование: activate_navi.bat [on|off]

if "%1"=="" (
    echo Использование: %0 [on^|off]
    echo   on  - активировать режим навигации ^(включить трансляцию^)
    echo   off - деактивировать режим навигации ^(выключить трансляцию^)
    exit /b 1
)

if /i "%1"=="on" (
    echo 🚀 ПРИНУДИТЕЛЬНАЯ АКТИВАЦИЯ ПРИБОРНОЙ ПАНЕЛИ...
    echo 📡 Отправка ПРЯМОЙ команды MCU: ID=557904571, VALUE=1
    adb shell am broadcast -a com.chinatsp.dashboard.FORCE_NAVI_ACTIVE --ez navi_state true
    echo ✅ КОМАНДА ОТПРАВЛЕНА: ПРИБОРКА ДОЛЖНА СТАТЬ ЧЕРНОЙ ^(РЕЖИМ ТРАНСЛЯЦИИ^)
    goto end
)

if /i "%1"=="off" (
    echo 🛑 ДЕАКТИВАЦИЯ ПРИБОРНОЙ ПАНЕЛИ...
    echo 📡 Отправка ПРЯМОЙ команды MCU: ID=557904571, VALUE=2
    adb shell am broadcast -a com.chinatsp.dashboard.FORCE_NAVI_ACTIVE --ez navi_state false
    echo ✅ КОМАНДА ОТПРАВЛЕНА: ПРИБОРКА ДОЛЖНА ВЕРНУТЬСЯ В ОБЫЧНЫЙ РЕЖИМ
    goto end
)

echo ❌ Неверный параметр: %1
echo Используйте 'on' или 'off'
exit /b 1

:end
echo.
echo 🎯 РЕЗУЛЬТАТ:
echo    📱 Приборная панель должна НЕМЕДЛЕННО переключиться
echo    🖤 При активации: приборка станет ЧЕРНОЙ ^(готова к трансляции^)
echo    📺 При деактивации: приборка вернется к обычному отображению
echo.
echo 🔍 МОНИТОРИНГ:
echo    📋 Логи: adb logcat ^| findstr "YandexNaviModel InteractiveModel forceRequestMeterNavi"
echo    🚗 MCU команды: adb logcat ^| findstr "setCabinIntValue 557904571"
echo.
echo ⚠️  ВАЖНО: Команда работает НЕЗАВИСИМО от навигационных приложений!
pause
