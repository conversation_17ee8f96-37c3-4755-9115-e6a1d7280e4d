.class public abstract Lcom/chinatsp/dashboard/model/DashboardModel;
.super Ljava/lang/Object;
.source "DashboardModel.java"

# interfaces
.implements Lcom/chinatsp/dashboard/callback/CarServiceCallback;
.implements Lcom/chinatsp/dashboard/callback/NaviCallback;


# static fields
.field private static final TAG:Ljava/lang/String; = "DashboardModel"


# instance fields
.field protected volatile mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

.field protected volatile mNaviModel:Lcom/chinatsp/dashboard/model/YandexNaviModel;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 15
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public appReturnToNormal()V
    .locals 2

    const-string v0, "DashboardModel"

    const-string v1, "appReturnToNormal"

    .line 98
    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 99
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    if-eqz v0, :cond_0

    .line 100
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    invoke-virtual {v0}, Lcom/chinatsp/dashboard/model/BaseServiceModel;->appReturnToNormal()V

    :cond_0
    return-void
.end method

.method public appShutdown()V
    .locals 2

    const-string v0, "DashboardModel"

    const-string v1, "appShutdown"

    .line 108
    invoke-static {v0, v1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 109
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    if-eqz v0, :cond_0

    .line 110
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    invoke-virtual {v0}, Lcom/chinatsp/dashboard/model/BaseServiceModel;->appShutdown()V

    :cond_0
    return-void
.end method

.method protected abstract createServiceModel(Landroid/content/Context;Lcom/chinatsp/dashboard/callback/CarServiceCallback;)Lcom/chinatsp/dashboard/model/BaseServiceModel;
.end method

.method public handleMediaInfo(IILjava/lang/String;Ljava/lang/String;)V
    .locals 0

    return-void
.end method

.method public handleMediaInfo(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;II)V
    .locals 0

    return-void
.end method

.method public handleMediaSource(Ljava/lang/String;)V
    .locals 0

    return-void
.end method

.method public naviStateChange(Z)V
    .locals 2

    .line 91
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "naviStateChange isNaviEnable:"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v0

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object p1

    const-string v0, " , mCarServiceModel:"

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object p1

    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "DashboardModel"

    invoke-static {v0, p1}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return-void
.end method

.method public onCarServiceConnected()V
    .locals 0

    .line 63
    invoke-static {}, Lcom/chinatsp/dashboard/utils/Logcat;->d()V

    return-void
.end method

.method public register(Landroid/content/Context;)V
    .locals 1

    .line 29
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    if-nez v0, :cond_0

    .line 30
    invoke-virtual {p0, p1, p0}, Lcom/chinatsp/dashboard/model/DashboardModel;->createServiceModel(Landroid/content/Context;Lcom/chinatsp/dashboard/callback/CarServiceCallback;)Lcom/chinatsp/dashboard/model/BaseServiceModel;

    move-result-object v0

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    .line 31
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    invoke-virtual {v0}, Lcom/chinatsp/dashboard/model/BaseServiceModel;->register()V

    .line 33
    :cond_0
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mNaviModel:Lcom/chinatsp/dashboard/model/YandexNaviModel;

    if-nez v0, :cond_1

    .line 34
    new-instance v0, Lcom/chinatsp/dashboard/model/YandexNaviModel;

    invoke-direct {v0, p1, p0}, Lcom/chinatsp/dashboard/model/YandexNaviModel;-><init>(Landroid/content/Context;Lcom/chinatsp/dashboard/callback/NaviCallback;)V

    iput-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mNaviModel:Lcom/chinatsp/dashboard/model/YandexNaviModel;

    .line 35
    iget-object p1, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mNaviModel:Lcom/chinatsp/dashboard/model/YandexNaviModel;

    invoke-virtual {p1}, Lcom/chinatsp/dashboard/model/YandexNaviModel;->init()V

    :cond_1
    return-void
.end method

.method public requestFitnessAnimChange(IZZZZIIIIIILjava/lang/String;Ljava/lang/String;)Z
    .locals 16

    move-object/from16 v1, p0

    .line 123
    monitor-enter p0

    .line 124
    :try_start_0
    iget-object v2, v1, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    .line 125
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v2, :cond_0

    move/from16 v3, p1

    move/from16 v4, p2

    move/from16 v5, p3

    move/from16 v6, p4

    move/from16 v7, p5

    move/from16 v8, p6

    move/from16 v9, p7

    move/from16 v10, p8

    move/from16 v11, p9

    move/from16 v12, p10

    move/from16 v13, p11

    move-object/from16 v14, p12

    move-object/from16 v15, p13

    .line 127
    invoke-virtual/range {v2 .. v15}, Lcom/chinatsp/dashboard/model/BaseServiceModel;->requestFitnessAnimChange(IZZZZIIIIIILjava/lang/String;Ljava/lang/String;)Z

    move-result v0

    const-string v2, "DashboardModel"

    .line 133
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "requestFitnessAnimChange fitnessType:"

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    move-result-object v3

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v2, v3}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    return v0

    :cond_0
    const-string v0, "DashboardModel"

    const-string v2, "requestFitnessAnimChange fail. mCarServiceModel is null. mFitnessBean:"

    .line 130
    invoke-static {v0, v2}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    const/4 v0, 0x0

    return v0

    :catchall_0
    move-exception v0

    .line 125
    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v0
.end method

.method public requestSendNaviInfo([I)V
    .locals 1

    .line 69
    monitor-enter p0

    .line 70
    :try_start_0
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    .line 71
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    .line 73
    invoke-virtual {v0, p1}, Lcom/chinatsp/dashboard/model/BaseServiceModel;->sendNaviInfo([I)V

    :cond_0
    return-void

    :catchall_0
    move-exception p1

    .line 71
    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public requestSendNextRoadName([B)V
    .locals 1

    .line 80
    monitor-enter p0

    .line 81
    :try_start_0
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    .line 82
    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    .line 84
    invoke-virtual {v0, p1}, Lcom/chinatsp/dashboard/model/BaseServiceModel;->sendNextRoadName([B)V

    :cond_0
    return-void

    :catchall_0
    move-exception p1

    .line 82
    :try_start_1
    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public unregister()V
    .locals 2

    .line 40
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    .line 41
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    invoke-virtual {v0}, Lcom/chinatsp/dashboard/model/BaseServiceModel;->unregister()V

    .line 42
    iput-object v1, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mCarServiceModel:Lcom/chinatsp/dashboard/model/BaseServiceModel;

    .line 44
    :cond_0
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mNaviModel:Lcom/chinatsp/dashboard/model/YandexNaviModel;

    if-eqz v0, :cond_1

    .line 45
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mNaviModel:Lcom/chinatsp/dashboard/model/YandexNaviModel;

    invoke-virtual {v0}, Lcom/chinatsp/dashboard/model/YandexNaviModel;->release()V

    .line 46
    iput-object v1, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mNaviModel:Lcom/chinatsp/dashboard/model/YandexNaviModel;

    :cond_1
    return-void
.end method

.method public forceNaviState(Z)V
    .locals 2

    .line 50
    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "forceNaviState: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Z)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    const-string v1, "DashboardModel"

    invoke-static {v1, v0}, Lcom/chinatsp/dashboard/utils/Logcat;->d(Ljava/lang/String;Ljava/lang/String;)V

    .line 51
    iget-object v0, p0, Lcom/chinatsp/dashboard/model/DashboardModel;->mNaviModel:Lcom/chinatsp/dashboard/model/YandexNaviModel;

    if-eqz v0, :cond_0

    .line 52
    invoke-virtual {v0, p1}, Lcom/chinatsp/dashboard/model/YandexNaviModel;->forceNaviActive(Z)V

    :cond_0
    return-void
.end method
